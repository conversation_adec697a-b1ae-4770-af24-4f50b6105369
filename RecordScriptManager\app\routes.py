# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   routes.py
@Time    :   2024/3/26 17:26
"""
import logging
from app import api
from app.resources.dataSpider.spider import A<PERSON><PERSON>ataSpider, SpiderControl, SpiderHistoryQuery, ExportControl, \
    ApplicationBaseSpider, ConfigManageAPI, AppDataUpdate
from app.resources.stream_recognition.stream_recognition import StreamRecognitionList, UploadPcap, PcapFileLink, \
    XdrDetailList, XdrDetailExport
from app.resources.user import UserResource
from app.resources.login import UserAuth
from app.resources.script_table import (Authors, EditScript, ExeScript, DelScript, ScriptRecord, Tasks, AppTable,
                                        ExtractAppTable, ScriptTable)
from app.resources.dashboard import ScriptNum, ScriptResult
from app.resources.upload import ScriptUpload, ScriptDownload, SnapshotDownload, ScriptUploadDownload
from app.resources.devices import DevicesManager, PriorityStatus
from app.resources.inspection import (Inspection, Inspection2, Inspection3, Inspection4, Inspection5, Inspection6,
                                      Inspection7, TaskLink, TaskTemplate)
from app.resources.inspection_enhanced import InspectionEnhanced
from app.resources.user_manage import UsersQuery, UserEdit, UserActivity, UserAdd, ScriptAllocation, UserDel, \
    PermissionList, PermissionAdd, PermissionEdit, PermissionDelete, PermissionAssign, RolePermissionList, RoleList, \
    UserPermissionList
import app.resources.feature.sniffer_version as sniffer_version
import app.resources.feature.sniffer_config as sniffer_config
import app.resources.feature.big_class as big_class
import app.resources.feature.sniffer_application as sniffer_application
from app.resources.dataSpider.rating import (
    RatingCalculator, ApplicationBaseSnapshotCompareAPI, ApplicationBaseSnapshotTypeListAPI,
    ApplicationBaseSnapshotCleanAPI
)


api.add_resource(UserResource, '/')
api.add_resource(UserAuth, '/token')

# api.add_resource(CnAndroidScript, '/cn_android')
# api.add_resource(EnAndroidScript, '/en_android')
# api.add_resource(CnIOSScript, '/cn_ios')
# api.add_resource(EnIOSScript, '/en_ios')
api.add_resource(ScriptTable, '/script_table')

api.add_resource(ScriptUpload, '/script_upload', resource_class_kwargs={"logger": logging.getLogger("root")})
api.add_resource(ScriptUploadDownload, '/upload_file_link')
api.add_resource(ScriptDownload, '/download/<script_type>/<l_class>')
api.add_resource(SnapshotDownload, '/download/<script_type>/<l_class>/<filename>')
api.add_resource(Authors, '/authors')
api.add_resource(EditScript, '/edit_script')
api.add_resource(ExeScript, '/exe_script')
api.add_resource(DelScript, '/del_script')
api.add_resource(ScriptRecord, "/script_record")
api.add_resource(AppTable, '/app_table')
api.add_resource(ExtractAppTable, '/download_app_table/<file_name>')

api.add_resource(ScriptNum, '/script_num')
api.add_resource(ScriptResult, '/script_result')

api.add_resource(DevicesManager, '/devices')
api.add_resource(PriorityStatus, '/priority')

api.add_resource(Inspection, '/inspection')
api.add_resource(InspectionEnhanced, '/inspection_enhanced')
api.add_resource(Inspection2, '/inspection_record')
api.add_resource(Inspection3, '/inspection_refresh')
api.add_resource(Inspection4, '/inspection_detail')
api.add_resource(Inspection5, '/inspection_status')
api.add_resource(Inspection6, '/inspection_delete')
api.add_resource(Inspection7, '/inspection_download')
api.add_resource(TaskLink, '/task_link')
api.add_resource(TaskTemplate, '/task_template')

api.add_resource(UsersQuery, '/user_query')
api.add_resource(UserEdit, '/user_edit')
api.add_resource(UserActivity, '/user_activity')
api.add_resource(UserAdd, '/user_add')
api.add_resource(ScriptAllocation, '/script_allocation')
api.add_resource(UserDel, '/user_del')

api.add_resource(PermissionList, '/permission_list')
api.add_resource(PermissionAdd, '/permission_add')
api.add_resource(PermissionEdit, '/permission_edit')
api.add_resource(PermissionDelete, '/permission_delete')
api.add_resource(PermissionAssign, '/permission_assign')
api.add_resource(RolePermissionList, '/role_permission_list')
api.add_resource(RoleList, '/role_list')
api.add_resource(UserPermissionList, '/user_role_permission_list')
api.add_resource(Tasks, '/tasks')

api.add_resource(sniffer_version.SnifferVersion, '/sniffer_version')
api.add_resource(sniffer_version.SnifferVersionAdd, '/sniffer_version_add')
api.add_resource(sniffer_version.SnifferVersionEdit, '/sniffer_version_edit')
api.add_resource(sniffer_version.SnifferVersionDel, '/sniffer_version_del')

api.add_resource(sniffer_config.SnifferConfig, '/sniffer_config')
api.add_resource(sniffer_config.SnifferConfigAdd, '/sniffer_config_add')
api.add_resource(sniffer_config.SnifferConfigEdit, '/sniffer_config_edit')
api.add_resource(sniffer_config.SnifferConfigDel, '/sniffer_config_del')

api.add_resource(sniffer_application.SnifferApplication, '/sniffer_application')
api.add_resource(sniffer_application.SnifferApplicationAdd, '/sniffer_application_add')
api.add_resource(sniffer_application.SnifferApplicationCheck, '/sniffer_application_check')
api.add_resource(sniffer_application.SnifferApplicationDel, '/sniffer_application_del')
api.add_resource(sniffer_application.SnifferMaxValues, '/sniffer_application_max')

api.add_resource(sniffer_application.ConvertSimplifiedChinese, '/sniffer_convert_simplified_chinese')

api.add_resource(sniffer_application.NtopLink, '/ntop_link')
api.add_resource(sniffer_application.NtopTemplate, '/ntop_template')

api.add_resource(sniffer_application.RjPublic, '/rj_public')

api.add_resource(sniffer_application.H3CPublic, '/h3c_public')
api.add_resource(sniffer_application.PublishFileLink, '/publish_file_link')
api.add_resource(sniffer_application.DbFileLink, '/db_file_link')
api.add_resource(sniffer_application.TaskProgress, '/task_progress')
api.add_resource(sniffer_application.CancelTask, '/cancel_task')

api.add_resource(sniffer_application.HistoryLogQuery, '/history_log')

api.add_resource(sniffer_application.VersioningQuery, '/versioning_query')
api.add_resource(sniffer_application.VersioningLatest, '/versioning_latest')
api.add_resource(sniffer_application.SpiderHistoryLatest, '/spider_history_latest')
api.add_resource(sniffer_application.SnifferPlatformSupportStats, '/sniffer_platform_support_stats')

api.add_resource(big_class.SnifferBigClass, '/sniffer_big_class')
api.add_resource(big_class.SnifferBigClassAdd, '/sniffer_big_class_add')
api.add_resource(big_class.SnifferBigClassEdit, '/sniffer_big_class_edit')
api.add_resource(big_class.SnifferBigClassDel, '/sniffer_big_class_del')
api.add_resource(big_class.IspCuccClass, '/isp_cucc_class')
api.add_resource(big_class.IspCtccClass, '/isp_ctcc_class')
api.add_resource(big_class.IspCmccClass, '/isp_cmcc_class')
api.add_resource(big_class.IspQoeCuccClass, '/isp_qoe_cucc_class')
api.add_resource(big_class.IspQoeCtccClass, '/isp_qoe_ctcc_class')
api.add_resource(big_class.IspQoeCmccClass, '/isp_qoe_cmcc_class')

api.add_resource(AppDataSpider, '/get_app_data')
api.add_resource(AppDataUpdate, '/update_app_data')
api.add_resource(SpiderControl, '/spider_control')
api.add_resource(SpiderHistoryQuery, '/spider_history_query')
api.add_resource(ExportControl, '/export_control')
api.add_resource(ApplicationBaseSpider, '/application_base_spider')

api.add_resource(ConfigManageAPI, '/store_config')

api.add_resource(RatingCalculator, '/calculate')
api.add_resource(ApplicationBaseSnapshotCompareAPI, '/snapshot_compare')
api.add_resource(ApplicationBaseSnapshotTypeListAPI, '/snapshot_type_list')
api.add_resource(ApplicationBaseSnapshotCleanAPI, '/snapshot_clean')

api.add_resource(UploadPcap, '/upload_pcap')
api.add_resource(StreamRecognitionList, '/stream_recognition_list')
api.add_resource(PcapFileLink, '/pcap_file_link')
api.add_resource(XdrDetailList, '/xdr_detail_list')
api.add_resource(XdrDetailExport, '/xdr_detail_export')