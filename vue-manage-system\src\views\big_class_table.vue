<template>
	<div>
		<div class="container">
			<el-tabs type="border-card">
				<el-tab-pane label="class">
					<div class="search-box">
						<el-select
							v-model="query.domestic"
							placeholder="状态"
							class="search-input mr10"
							>
							<el-option
								v-for="item in domestic_options"
									:key="item.value"
									:label="item.label"
									:value="item.value"
							/>
						</el-select>
						<el-input class="value-input mr10" clearable v-model="query.value"/>
						<el-button type="primary" :icon="DocumentAdd" @click="openAddBigClass">新增</el-button>
						<el-button type="info" :icon="Search" @click="handleSearch">搜索</el-button>
					</div>
					
					<el-table :data="tableData" border class="table" ref="multipleTable" header-cell-class-name="table-header">
						
						<el-table-column prop="id" label="id" align="center"></el-table-column>

						<el-table-column prop="class" label="class" align="center"></el-table-column>
						<el-table-column prop="classname" label="classname" align="center"></el-table-column>
						<el-table-column prop="enclassname" label="enclassname" align="center"></el-table-column>
						
						<el-table-column fixed="right" label="操作" width="200" align="center">
							<template #default="scope">
								<el-button
									type="primary"
									@click="openEditBigClass(scope.row)"
								>
									编辑
								</el-button>
								<el-button
									type="danger"
									@click="handleDelBigClass(scope.row)"
								>
									删除
								</el-button>
							</template>
						</el-table-column>
					</el-table>
					<div class="pagination">
						<el-pagination
							background
							layout="total, prev, pager, next"
							:current-page="query.pageIndex"
							:page-size="query.pageSize"
							:total="pageTotal"
							@current-change="handlePageChange"
						></el-pagination>
					</div>
				</el-tab-pane>

				<el-tab-pane label="cucc">
					<el-table :data="cuccData" border class="table" ref="multipleTable" header-cell-class-name="table-header">
						<el-table-column prop="cucc_type" label="序号" align="center" width="120px"></el-table-column>
						<el-table-column prop="cucc_type_name" label="协议大类" align="center"></el-table-column>
						<el-table-column prop="cucc_content" label="协议内容" align="center"></el-table-column>
					</el-table>
				</el-tab-pane>

				<el-tab-pane label="ctcc">
					<el-table :data="ctccData" border class="table" ref="multipleTable" header-cell-class-name="table-header">
						<el-table-column prop="ctcc_type" label="序号" align="center" width="120px"></el-table-column>
						<el-table-column prop="ctcc_type_name" label="协议大类" align="center"></el-table-column>
						<el-table-column prop="ctcc_content" label="协议内容" align="center"></el-table-column>
					</el-table>
				</el-tab-pane>

				<el-tab-pane label="cmcc">
					<el-table :data="cmccData" border class="table" ref="multipleTable" header-cell-class-name="table-header">
						<el-table-column prop="cmcc_type" label="序号" align="center" width="120px"></el-table-column>
						<el-table-column prop="cmcc_type_name" label="协议大类" align="center"></el-table-column>
						<el-table-column prop="cmcc_content" label="协议内容" align="center"></el-table-column>
					</el-table>
				</el-tab-pane>

				<el-tab-pane label="qoe-cucc">
					<el-table :data="cuccQoeData" border class="table" ref="multipleTable" header-cell-class-name="table-header">
						<el-table-column prop="cucc_qoe_value" label="cucc_qoe_value" align="center"></el-table-column>
						<el-table-column prop="cucc_qoe_type" label="cucc_qoe_type" align="center"></el-table-column>
						<el-table-column prop="cucc_qoe_name" label="cucc_qoe_name" align="center"></el-table-column>
					</el-table>
				</el-tab-pane>

				<el-tab-pane label="qoe-ctcc">
					<el-table :data="ctccQoeData" border class="table" ref="multipleTable" header-cell-class-name="table-header">
						<el-table-column prop="ctcc_qoe_value" label="ctcc_qoe_value" align="center"></el-table-column>
						<el-table-column prop="ctcc_qoe_type" label="ctcc_qoe_type" align="center"></el-table-column>
						<el-table-column prop="ctcc_qoe_name" label="ctcc_qoe_name" align="center"></el-table-column>
					</el-table>
				</el-tab-pane>

				<el-tab-pane label="qoe-cmcc">
					<el-table :data="cmccQoeData" border class="table" ref="multipleTable" header-cell-class-name="table-header">
						<el-table-column prop="cmcc_qoe_value" label="cmcc_qoe_value" align="center"></el-table-column>
						<el-table-column prop="cmcc_qoe_type" label="cmcc_qoe_type" align="center"></el-table-column>
						<el-table-column prop="cmcc_qoe_name" label="cmcc_qoe_name" align="center"></el-table-column>
					</el-table>
				</el-tab-pane>

			</el-tabs>

			<el-dialog
				v-model="bigClassDelVisible"
				title="警告"
				width="500"
				align-center
				>
				<span>确定要删除大类 {{ bigClassDelForm.class }} 吗？</span>
				<template #footer>
					<div class="dialog-footer">
						<el-button type="danger" :icon="Delete" @click="handleBigClassDel">删除</el-button>
						<el-button :icon="CloseBold" @click="bigClassDelVisible = false">取消</el-button>
					</div>
				</template>
			</el-dialog>

			<el-dialog 
				v-model="bigClassDialogVisible" 
				:title="bigClassDialogMode === 'add' ? '新增大类' : '编辑大类'" 
				style="max-width: 500px;"
				>
				<el-form 
					:model="bigClassForm"
					label-width="auto"
					>
					<el-form-item v-if="bigClassDialogMode === 'add'" label="区域">
						<el-select
							v-model="bigClassForm.domestic"
							placeholder="状态"
							class="search-input mr10"
							>
							<el-option
								v-for="item in domestic_options"
									:key="item.value"
									:label="item.label"
									:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="class">
					<el-input-number 
						v-model="bigClassForm.class" 
						:min="701" 
						:max="799"
						:class="{ 'modified-input': isFieldModified('class') }" 
						:controls-position="'both'"
						style="width: 100%;"
					/>
					</el-form-item>
					<el-form-item label="classname">
						<el-input v-model="bigClassForm.classname" :class="{ 'modified-input': isFieldModified('classname') }" />
					</el-form-item>
					<el-form-item label="enclassname">
						<el-input v-model="bigClassForm.enclassname" :class="{ 'modified-input': isFieldModified('enclassname') }" />
					</el-form-item>

				</el-form>
				<template #footer>
					<el-button type="primary" :icon="bigClassDialogMode === 'add' ? Promotion : EditPen" @click="handleBigClassDialogSubmit">提交</el-button>
					<el-button :icon="CloseBold" @click="bigClassDialogVisible = false">取消</el-button>
				</template>
			</el-dialog>
			
		</div>
	</div>
</template>

<script setup lang="ts" name="version_table_basetable">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, DocumentAdd, Promotion, CloseBold, EditPen, Delete } from '@element-plus/icons-vue';
import { snfBigClass, snfBigClassAdd, snfBigClassEdit, snfBigClassDel, snfCuccClass, snfCtccClass, 
	snfCmccClass, snfQoeCuccClass, snfCtccQoeClass, snfCmccQoeClass } from '../api/index';


interface TableItem {
	id: number;
	domestic: number;
	class: string;
	classname: string;
	enclassname: string;
}

interface CuccItem {
	id: number;
	cucc_type: number;
	cucc_type_name: string;
	cucc_content: string;	
}

interface CuccQoeItem {
	id: number;
	cucc_qoe_value: number;
	cucc_qoe_type: string;
	cucc_qoe_name: string;	
}

interface CtccItem {
	id: number;
	ctcc_type: number;
	ctcc_type_name: string;
	ctcc_content: string;	
}

interface CtccQoeItem {
	id: number;
	ctcc_qoe_value: number;
	ctcc_qoe_type: string;
	ctcc_qoe_name: string;	
}

interface CmccItem {
	id: number;
	cmcc_type: number;
	cmcc_type_name: string;
	cmcc_content: string;	
}

interface CmccQoeItem {
	id: number;
	cmcc_qoe_value: number;
	cmcc_qoe_type: string;
	cmcc_qoe_name: string;	
}

const query = reactive({
	domestic: 0,
	value: '',
	pageIndex: 1,
	pageSize: 10
});
const tableData = ref<TableItem[]>([]);
const originalTableData = ref<TableItem[]>([]);
const cuccData = ref<CuccItem[]>([]);
const ctccData = ref<CtccItem[]>([]);
const cmccData = ref<CmccItem[]>([]);
const cuccQoeData = ref<CuccQoeItem[]>([]);
const ctccQoeData = ref<CtccQoeItem[]>([]);
const cmccQoeData = ref<CmccQoeItem[]>([]);
const pageTotal = ref(0);

const domestic_options = [
	{
    	value: 0,
    	label: '国内',
  	},
	{
		value: 1,
		label: '国外',
	}
]

const getSnfBigClassData = async (tableInfo: any) => {
	try {
		const res = await snfBigClass(tableInfo);
		if (res.data.code == 200) {
			tableData.value = res.data.list;
            // 保存原始数据副本
            originalTableData.value = JSON.parse(JSON.stringify(res.data.list));
			pageTotal.value = res.data.total;
		} else {
			ElMessage.error(res.data.message)
		}
		
	} catch (err) {
		ElMessage.error('获取大类数据异常！');
	}
}

const getCuccData = async () => {
	try {
		const res = await snfCuccClass();
		if (res.data.code == 200) {
			cuccData.value = res.data.cucc_class;
		} else {
			ElMessage.error(res.data.message);
		}
		
	} catch (err) {
		ElMessage.error('获取cucc数据异常！');
	}
}

const getCuccQoeData = async () => {
	try {
		const res = await snfQoeCuccClass();
		if (res.data.code == 200) {
			cuccQoeData.value = res.data.qoe_cucc_class;
		} else {
			ElMessage.error(res.data.message);
		}
		
	} catch (err) {
		ElMessage.error('获取qoe-cucc数据异常！');
	}
}


const getCtccData = async () => {
	try {
		const res = await snfCtccClass();
		if (res.data.code == 200) {
			ctccData.value = res.data.ctcc_class;
		} else {
			ElMessage.error(res.data.message);
		}
		
	} catch (err) {
		ElMessage.error('获取ctcc数据异常！');
	}
}

const getCtccQoeData = async () => {
	try {
		const res = await snfCtccQoeClass();
		if (res.data.code == 200) {
			ctccQoeData.value = res.data.qoe_ctcc_class;
		} else {
			ElMessage.error(res.data.message);
		}
		
	} catch (err) {
		ElMessage.error('获取qoe-ctcc数据异常！');
	}
}

const getCmccData = async () => {
	try {
		const res = await snfCmccClass();
		if (res.data.code == 200) {
			cmccData.value = res.data.cmcc_class;
		} else {
			ElMessage.error(res.data.message);
		}
		
	} catch (err) {
		ElMessage.error('获取cmcc数据异常！');
	}
}


const getCmccQoeData = async () => {
	try {
		const res = await snfCmccQoeClass();
		if (res.data.code == 200) {
			cmccQoeData.value = res.data.qoe_cmcc_class;
		} else {
			ElMessage.error(res.data.message);
		}
		
	} catch (err) {
		ElMessage.error('获取qoe-cmcc数据异常！');
	}
}


getSnfBigClassData(query);
getCuccData();
getCtccData();
getCmccData();
getCuccQoeData();
getCtccQoeData();
getCmccQoeData();



// 查询操作
const handleSearch = () => {
	query.pageIndex = 1;
	getSnfBigClassData(query);
};
// 分页导航
const handlePageChange = (val: number) => {
	query.pageIndex = val;
	getSnfBigClassData(query);
};


// 删除版本配置
const bigClassDelVisible = ref(false);
const bigClassDelForm = reactive({
	id: 0,
	class: ""
});

const handleDelBigClass = (row: TableItem) => {
	bigClassDelVisible.value = true;
	bigClassDelForm.id = row.id;
	bigClassDelForm.class = row.class;
}


const bigClassDelSubmit = async () => {
	try {
		const rsp = await snfBigClassDel(bigClassDelForm);
		if (rsp.data.code == 200) {
			ElMessage.success(rsp.data.message);
			query.pageIndex = 1;
			bigClassDelVisible.value = false;
			getSnfBigClassData(query);
		} else {
			ElMessage.error(rsp.data.message);
		}
	} catch (err) {
		ElMessage.error("删除大类数据异常！");
	}
}

const handleBigClassDel = () => {
	bigClassDelSubmit();
}


const bigClassDialogVisible = ref(false);
const bigClassDialogMode = ref<'add' | 'edit'>('add');
const bigClassForm = reactive({
	id: 0,
	domestic: 0,
	class: '',
	classname: '',
	enclassname: '',
});

// 保存原始数据用于高亮对比
const originalBigClassData = ref({
  id: 0,
  domestic: 0,
  class: '',
  classname: '',
  enclassname: ''
});

const openAddBigClass = () => {
  bigClassDialogMode.value = 'add';
  bigClassDialogVisible.value = true;
  bigClassForm.id = 0;
  bigClassForm.domestic = 0;
  bigClassForm.class = '';
  bigClassForm.classname = '';
  bigClassForm.enclassname = '';
  originalBigClassData.value = { ...bigClassForm };
};

const openEditBigClass = (row: TableItem) => {
  bigClassDialogMode.value = 'edit';
  bigClassDialogVisible.value = true;
  bigClassForm.id = row.id;
  bigClassForm.domestic = row.domestic;
  bigClassForm.class = row.class;
  bigClassForm.classname = row.classname;
  bigClassForm.enclassname = row.enclassname;
  originalBigClassData.value = { ...row };
};

const handleBigClassDialogSubmit = async () => {
	try {
		let rsp;
		if (bigClassDialogMode.value === 'add') {
			rsp = await snfBigClassAdd(bigClassForm);
		} else {
			rsp = await snfBigClassEdit(bigClassForm);
		}
		if (rsp.data.code == 200) {
			ElMessage.success(rsp.data.message);
			query.pageIndex = 1;
			bigClassDialogVisible.value = false;
			// 记录刚刚编辑的id
			if (bigClassDialogMode.value === 'edit') {
				// lastEditedId.value = bigClassForm.id; // This line is removed
				// setTimeout(() => { lastEditedId.value = null; }, 3000); // This line is removed
			}
			getSnfBigClassData(query);
		} else {
			ElMessage.error(rsp.data.message);
		}
	} catch (err) {
		ElMessage.error(bigClassDialogMode.value === 'add' ? '新增大类数据异常！' : '编辑大类数据异常！');
	}
};

const isFieldModified = (field: keyof typeof bigClassForm) => {
  if (bigClassDialogMode.value === 'add') {
    return bigClassForm[field] !== '';
  }
  return bigClassForm[field] !== originalBigClassData.value[field];
};

</script>

<style scoped>
.search-box {
	margin-bottom: 10px;
}

.search-input {
	width: 120px;
}

.value-input {
	width: 240px;
}

.mr10 {
	margin-right: 10px;
}

.mrl10 {
	margin-left: 10px;
}

.table-td-thumb {
	display: block;
	margin: auto;
	width: 40px;
	height: 40px;
}

.snapshot-image .block {
  padding: 10px 0;
  text-align: center;
  border-right: solid 1px var(--el-border-color);
  display: inline-block;
  width: 20%;
  box-sizing: border-box;
  vertical-align: top;
}

.snapshot-image .block:last-child {
  border-right: none;
}

.snapshot-image .snapstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.modified-field {
  background-color: #fff3e6 !important;
  border-radius: 4px;
  padding: 0 4px;
}

.modified-input :deep(.el-input__wrapper) {
  background-color: #fff3e6 !important;
}

</style>
