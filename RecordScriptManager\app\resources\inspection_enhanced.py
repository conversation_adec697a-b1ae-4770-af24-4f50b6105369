# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   inspection_enhanced.py
@Time    :   2024/4/11 13:18
"""
import os
import datetime
from flask import jsonify, url_for, send_file
from flask_restful import Resource, reqparse
from werkzeug.datastructures import FileStorage
from sqlalchemy import and_
from app import db
from app.models import CnAndroid, EnAndroid, CnIOS, EnIOS, InspectionRecord, Task, TaskRecord, User
from app.resources.user import authenticate
from app.utils import work_update, task_template, read_task_file, write_task_detail_table
from config import Config


class InspectionEnhanced(Resource):
    method_decorators = [authenticate]

    def get(self):
        pass

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, location="form")
        parser.add_argument("platform", type=str, location="form")
        parser.add_argument("mode", type=str, location="form")
        parser.add_argument('file', type=FileStorage, location='files')
        args = parser.parse_args()
        name = args.get("name")
        platforms = args.get("platform", "")
        mode = args.get("mode", "")
        _file = args.get('file')
        
        # 参数验证
        if not name or (not platforms and not _file):
            return {"code": 400, "message": "参数错误"}
        if mode not in ['block', 'identify']:
            return {"code": 400, "message": "mode参数必须是block或identify"}
            
        _tmp = Task.query.filter_by(name=name).first()
        if _tmp:
            return {"code": 400, "message": "任务名称重复！"}
            
        _task = Task(name=name, mode=mode)
        db.session.add(_task)
        db.session.commit()
        
        if platforms:
            pf = platforms.split("+")
            for item in pf:
                if item == Config.CN_ANDROID:
                    self.inspect_cn_android_enhanced(args)
                if item == Config.EN_ANDROID:
                    self.inspect_en_android_enhanced(args)
                if item == Config.CN_IOS:
                    self.inspect_cn_ios_enhanced(args)
                if item == Config.EN_IOS:
                    self.inspect_en_ios_enhanced(args)
        if _file:
            _f = self._file_task_enhanced(_file, name, mode)
            if not _f:
                return {"code": 400, "message": "任务文件异常"}
        return {"code": 200, "message": f"新增日常巡检成功！"}

    def _file_task_enhanced(self, _file: FileStorage, task_name: str, mode: str):
        try:
            filename = f"{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}-" + _file.filename
            path = os.path.join(Config.BASEDIR, 'tmp', filename)
            _file.save(path)
        except Exception as err:
            return False
        try:
            data = read_task_file(path)
            if (not data[Config.CN_ANDROID] and not data[Config.EN_ANDROID] and
                    not data[Config.CN_IOS] and not data[Config.EN_IOS]):
                return True
            task = Task.query.filter_by(name=task_name).first()
            cn_android_total, en_android_total, cn_ios_total, en_ios_total = task.cn_android_total, task.en_android_total, task.cn_ios_total, task.en_ios_total
            
            # 根据mode设置查询条件
            if mode == 'block':
                cn_android_condition = and_(CnAndroid.l_class.in_(data[Config.CN_ANDROID]), CnAndroid.block_script == 1)
                en_android_condition = and_(EnAndroid.l_class.in_(data[Config.EN_ANDROID]), EnAndroid.block_script == 1)
                cn_ios_condition = and_(CnIOS.l_class.in_(data[Config.CN_IOS]), CnIOS.block_script == 1)
                en_ios_condition = and_(EnIOS.l_class.in_(data[Config.EN_IOS]), EnIOS.block_script == 1)
            else:  # identify
                cn_android_condition = and_(CnAndroid.l_class.in_(data[Config.CN_ANDROID]), CnAndroid.identify_script == 1)
                en_android_condition = and_(EnAndroid.l_class.in_(data[Config.EN_ANDROID]), EnAndroid.identify_script == 1)
                cn_ios_condition = and_(CnIOS.l_class.in_(data[Config.CN_IOS]), CnIOS.identify_script == 1)
                en_ios_condition = and_(EnIOS.l_class.in_(data[Config.EN_IOS]), EnIOS.identify_script == 1)
            
            for item in data[Config.CN_ANDROID]:
                ca = CnAndroid.query.filter(and_(CnAndroid.l_class == item, 
                    CnAndroid.block_script == 1 if mode == 'block' else CnAndroid.identify_script == 1)).first()
                if ca:
                    cn_android_total += 1
                    tr = TaskRecord(appid=ca.id, platform=1)
                    task.task_record.append(tr)
            task.cn_android_total = cn_android_total
            
            for item in data[Config.EN_ANDROID]:
                ea = EnAndroid.query.filter(and_(EnAndroid.l_class == item,
                    EnAndroid.block_script == 1 if mode == 'block' else EnAndroid.identify_script == 1)).first()
                if ea:
                    en_android_total += 1
                    tr = TaskRecord(appid=ea.id, platform=2)
                    task.task_record.append(tr)
            task.en_android_total = en_android_total
            
            for item in data[Config.CN_IOS]:
                ci = CnIOS.query.filter(and_(CnIOS.l_class == item,
                    CnIOS.block_script == 1 if mode == 'block' else CnIOS.identify_script == 1)).first()
                if ci:
                    cn_ios_total += 1
                    tr = TaskRecord(appid=ci.id, platform=3)
                    task.task_record.append(tr)
            task.cn_ios_total = cn_ios_total
            
            for item in data[Config.EN_IOS]:
                ei = EnIOS.query.filter(and_(EnIOS.l_class == item,
                    EnIOS.block_script == 1 if mode == 'block' else EnIOS.identify_script == 1)).first()
                if ei:
                    en_ios_total += 1
                    tr = TaskRecord(appid=ei.id, platform=4)
                    task.task_record.append(tr)
            task.en_ios_total = en_ios_total
            
            db.session.add(task)
            db.session.commit()
            return True
        except Exception as err:
            return False

    def inspect_cn_android_enhanced(self, args: dict):
        data = list()
        name = args.get("name")
        mode = args.get("mode")
        
        # 根据mode设置查询条件
        if mode == 'block':
            cas = CnAndroid.query.filter(and_(CnAndroid.result == "成功", CnAndroid.delete == 0, CnAndroid.block_script == 1)).all()
        else:  # identify
            cas = CnAndroid.query.filter(and_(CnAndroid.result == "成功", CnAndroid.delete == 0, CnAndroid.identify_script == 1)).all()
            
        task = Task.query.filter_by(name=name).first()
        task.cn_android_total = len(cas)
        for item in cas:
            tr = TaskRecord(appid=item.id, platform=1)
            task.task_record.append(tr)
        data.append(task)
        db.session.add_all(data)
        db.session.commit()
        return {"code": 200, "message": f"新增日常巡检成功！"}

    def inspect_en_android_enhanced(self, args: dict):
        data = list()
        name = args.get("name")
        mode = args.get("mode")
        
        # 根据mode设置查询条件
        if mode == 'block':
            cas = EnAndroid.query.filter(and_(EnAndroid.result == "成功", EnAndroid.delete == 0, EnAndroid.block_script == 1)).all()
        else:  # identify
            cas = EnAndroid.query.filter(and_(EnAndroid.result == "成功", EnAndroid.delete == 0, EnAndroid.identify_script == 1)).all()
            
        task = Task.query.filter_by(name=name).first()
        task.en_android_total = len(cas)
        for item in cas:
            tr = TaskRecord(appid=item.id, platform=2)
            task.task_record.append(tr)
        data.append(task)
        db.session.add_all(data)
        db.session.commit()
        return {"code": 200, "message": f"新增日常巡检成功！"}

    def inspect_cn_ios_enhanced(self, args: dict):
        data = list()
        name = args.get("name")
        mode = args.get("mode")
        
        # 根据mode设置查询条件
        if mode == 'block':
            cas = CnIOS.query.filter(and_(CnIOS.result == "成功", CnIOS.delete == 0, CnIOS.block_script == 1)).all()
        else:  # identify
            cas = CnIOS.query.filter(and_(CnIOS.result == "成功", CnIOS.delete == 0, CnIOS.identify_script == 1)).all()
            
        task = Task.query.filter_by(name=name).first()
        task.cn_ios_total = len(cas)
        for item in cas:
            tr = TaskRecord(appid=item.id, platform=3)
            task.task_record.append(tr)
        data.append(task)
        db.session.add_all(data)
        db.session.commit()
        return {"code": 200, "message": f"新增日常巡检成功！"}

    def inspect_en_ios_enhanced(self, args: dict):
        data = list()
        name = args.get("name")
        mode = args.get("mode")
        
        # 根据mode设置查询条件
        if mode == 'block':
            cas = EnIOS.query.filter(and_(EnIOS.result == "成功", EnIOS.delete == 0, EnIOS.block_script == 1)).all()
        else:  # identify
            cas = EnIOS.query.filter(and_(EnIOS.result == "成功", EnIOS.delete == 0, EnIOS.identify_script == 1)).all()
            
        task = Task.query.filter_by(name=name).first()
        task.en_ios_total = len(cas)
        for item in cas:
            tr = TaskRecord(appid=item.id, platform=4)
            task.task_record.append(tr)
        data.append(task)
        db.session.add_all(data)
        db.session.commit()
        return {"code": 200, "message": f"新增日常巡检成功！"}
