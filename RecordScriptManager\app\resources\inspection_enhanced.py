# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   inspection_enhanced.py
@Time    :   2024/4/11 13:18
"""
import os
import datetime
import logging
from flask_restful import Resource, reqparse
from werkzeug.datastructures import FileStorage
from sqlalchemy import and_
from app import db
from app.models import CnAndroid, EnAndroid, CnIOS, EnIOS, Task, TaskRecord
from app.resources.user import authenticate
from app.utils import read_task_file
from config import Config



class Inspection(Resource):
    method_decorators = [authenticate]

    @staticmethod
    def _parse_arguments():
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, location="form")
        parser.add_argument("platform", type=str, location="form")
        parser.add_argument("mode", type=str, location="form")
        parser.add_argument('file', type=FileStorage, location='files')
        args = parser.parse_args()

        return {
            'name': args.get("name"),
            'platforms': args.get("platform", ""),
            'mode': args.get("mode", ""),
            'file': args.get('file')
        }

    def post(self):
        try:
            args = self._parse_arguments()

            validation_result = self._validate_parameters(args)
            if validation_result:
                return validation_result

            task = self._create_task(args['name'], args['mode'])

            if args['platforms']:
                self._process_platform_tasks(args, task)

            if args['file']:
                file_result = self._process_file_task(args['file'], args['mode'], task)
                if not file_result:
                    db.session.rollback()
                    return {"code": 400, "message": "任务文件异常"}

            db.session.commit()
            return {"code": 200, "message": "新增日常巡检成功！"}

        except Exception as e:
            db.session.rollback()
            logging.error(f"创建巡检任务失败: {str(e)}")
            return {"code": 500, "message": "数据库操作异常"}



    @staticmethod
    def _validate_parameters(args):
        if not args['name'] or (not args['platforms'] and not args['file']):
            return {"code": 400, "message": "参数错误"}

        if args['mode'] not in ['block', 'identify']:
            return {"code": 400, "message": "mode参数必须是block或identify"}

        existing_task = Task.query.filter_by(name=args['name']).first()
        if existing_task:
            return {"code": 400, "message": "任务名称重复！"}

        return None

    @staticmethod
    def _create_task(name, mode):
        task = Task(name=name, mode=mode)
        db.session.add(task)
        db.session.flush()
        return task

    @staticmethod
    def _build_query_conditions(model, mode, l_class=None):
        conditions = [model.result == "成功", model.delete == 0]
        if l_class is not None:
            conditions.append(model.l_class == l_class)

        if mode == 'block':
            conditions.append(model.block_script == 1)
        else:
            conditions.append(model.identify_script == 1)

        return and_(*conditions)

    @staticmethod
    def _create_task_records(items, platform_id, task):
        for item in items:
            task_record = TaskRecord(appid=item.id, platform=platform_id)
            task.task_record.append(task_record)

    @staticmethod
    def _get_platform_configs():
        return {
            Config.CN_ANDROID: {
                'model': CnAndroid,
                'platform_id': 1,
                'total_field': 'cn_android_total'
            },
            Config.EN_ANDROID: {
                'model': EnAndroid,
                'platform_id': 2,
                'total_field': 'en_android_total'
            },
            Config.CN_IOS: {
                'model': CnIOS,
                'platform_id': 3,
                'total_field': 'cn_ios_total'
            },
            Config.EN_IOS: {
                'model': EnIOS,
                'platform_id': 4,
                'total_field': 'en_ios_total'
            }
        }

    def _process_platform_generic(self, platform_key, mode, task):
        platform_configs = self._get_platform_configs()
        config = platform_configs[platform_key]
        model = config['model']
        platform_id = config['platform_id']
        total_field = config['total_field']

        conditions = self._build_query_conditions(model, mode)
        items = model.query.filter(conditions).all()

        setattr(task, total_field, len(items))
        self._create_task_records(items, platform_id, task)

    def _process_platform_tasks(self, args, task):
        platforms = args['platforms'].split("+")
        for platform_key in platforms:
            if platform_key in [Config.CN_ANDROID, Config.EN_ANDROID, Config.CN_IOS, Config.EN_IOS]:
                self._process_platform_generic(platform_key, args['mode'], task)



    def _process_file_task(self, file, mode, task):
        try:
            file_path = self._save_uploaded_file(file)
            if not file_path:
                return False
            data = read_task_file(file_path)
            if not self._has_valid_data(data):
                return True

            self._process_file_platforms(data, mode, task)

            return True

        except Exception as e:
            logging.error(f"处理文件任务失败: {str(e)}")
            return False

    @staticmethod
    def _save_uploaded_file(file):
        try:
            filename = f"{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}-" + file.filename
            path = os.path.join(Config.BASEDIR, 'tmp', filename)
            file.save(path)
            return path
        except Exception as e:
            logging.error(f"保存文件失败: {str(e)}")
            return None

    @staticmethod
    def _has_valid_data(data):
        return (data[Config.CN_ANDROID] or data[Config.EN_ANDROID] or
                data[Config.CN_IOS] or data[Config.EN_IOS])

    def _process_file_platforms(self, data, mode, task):
        platform_keys = [Config.CN_ANDROID, Config.EN_ANDROID, Config.CN_IOS, Config.EN_IOS]
        for platform_key in platform_keys:
            if data[platform_key]:
                self._process_file_platform_generic(data[platform_key], platform_key, mode, task)

    def _process_file_platform_generic(self, l_class_list, platform_key, mode, task):
        platform_configs = self._get_platform_configs()
        config = platform_configs[platform_key]
        model = config['model']
        platform_id = config['platform_id']
        total_field = config['total_field']

        current_total = getattr(task, total_field)

        for l_class in l_class_list:
            conditions = self._build_query_conditions(model, mode, l_class)
            item = model.query.filter(conditions).first()

            if item:
                current_total += 1
                task_record = TaskRecord(appid=item.id, platform=platform_id)
                task.task_record.append(task_record)

        setattr(task, total_field, current_total)
