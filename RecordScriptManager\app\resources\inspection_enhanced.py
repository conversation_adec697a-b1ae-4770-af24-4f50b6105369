# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   inspection_enhanced.py
@Time    :   2024/4/11 13:18
"""
import os
import datetime
import logging
from typing import Dict, List, Optional, Tuple, Any
from flask import jsonify, url_for, send_file
from flask_restful import Resource, reqparse
from werkzeug.datastructures import FileStorage
from sqlalchemy import and_
from app import db
from app.models import CnAndroid, EnAndroid, CnIOS, EnIOS, InspectionRecord, Task, TaskRecord, User
from app.resources.user import authenticate
from app.utils import work_update, task_template, read_task_file, write_task_detail_table
from config import Config

# 常量定义
BLOCK_MODE = 'block'
IDENTIFY_MODE = 'identify'
VALID_MODES = [BLOCK_MODE, IDENTIFY_MODE]

# 平台配置映射
PLATFORM_CONFIG = {
    Config.CN_ANDROID: {
        'model': CnAndroid,
        'platform_id': 1,
        'total_field': 'cn_android_total'
    },
    Config.EN_ANDROID: {
        'model': EnAndroid,
        'platform_id': 2,
        'total_field': 'en_android_total'
    },
    Config.CN_IOS: {
        'model': CnIOS,
        'platform_id': 3,
        'total_field': 'cn_ios_total'
    },
    Config.EN_IOS: {
        'model': EnIOS,
        'platform_id': 4,
        'total_field': 'en_ios_total'
    }
}

# 错误消息常量
ERROR_MESSAGES = {
    'INVALID_PARAMS': '参数错误',
    'INVALID_MODE': f'mode参数必须是{BLOCK_MODE}或{IDENTIFY_MODE}',
    'DUPLICATE_TASK': '任务名称重复！',
    'FILE_ERROR': '任务文件异常',
    'DB_ERROR': '数据库操作异常'
}

# 成功消息常量
SUCCESS_MESSAGE = '新增日常巡检成功！'


class InspectionEnhanced(Resource):
    method_decorators = [authenticate]

    def get(self):
        pass

    def post(self) -> Dict[str, Any]:
        """创建增强版巡检任务"""
        try:
            # 解析参数
            args = self._parse_arguments()

            # 验证参数
            validation_result = self._validate_parameters(args)
            if validation_result:
                return validation_result

            # 创建任务
            task = self._create_task(args['name'], args['mode'])

            # 处理平台任务
            if args['platforms']:
                self._process_platform_tasks(args, task)

            # 处理文件任务
            if args['file']:
                file_result = self._process_file_task(args['file'], args['mode'], task)
                if not file_result:
                    db.session.rollback()
                    return {"code": 400, "message": ERROR_MESSAGES['FILE_ERROR']}

            db.session.commit()
            return {"code": 200, "message": SUCCESS_MESSAGE}

        except Exception as e:
            db.session.rollback()
            logging.error(f"创建巡检任务失败: {str(e)}")
            return {"code": 500, "message": ERROR_MESSAGES['DB_ERROR']}

    def _parse_arguments(self) -> Dict[str, Any]:
        """解析请求参数"""
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, location="form")
        parser.add_argument("platform", type=str, location="form")
        parser.add_argument("mode", type=str, location="form")
        parser.add_argument('file', type=FileStorage, location='files')
        args = parser.parse_args()

        return {
            'name': args.get("name"),
            'platforms': args.get("platform", ""),
            'mode': args.get("mode", ""),
            'file': args.get('file')
        }

    def _validate_parameters(self, args: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """验证参数有效性"""
        if not args['name'] or (not args['platforms'] and not args['file']):
            return {"code": 400, "message": ERROR_MESSAGES['INVALID_PARAMS']}

        if args['mode'] not in VALID_MODES:
            return {"code": 400, "message": ERROR_MESSAGES['INVALID_MODE']}

        # 检查任务名称重复
        existing_task = Task.query.filter_by(name=args['name']).first()
        if existing_task:
            return {"code": 400, "message": ERROR_MESSAGES['DUPLICATE_TASK']}

        return None

    def _create_task(self, name: str, mode: str) -> Task:
        """创建任务记录"""
        task = Task(name=name, mode=mode)
        db.session.add(task)
        db.session.flush()  # 获取task.id但不提交事务
        return task

    def _process_platform_tasks(self, args: Dict[str, Any], task: Task) -> None:
        """处理平台任务"""
        platforms = args['platforms'].split("+")
        for platform_key in platforms:
            if platform_key in PLATFORM_CONFIG:
                self._process_single_platform(platform_key, args['mode'], task)

    def _process_single_platform(self, platform_key: str, mode: str, task: Task) -> None:
        """处理单个平台的任务"""
        config = PLATFORM_CONFIG[platform_key]
        model = config['model']
        platform_id = config['platform_id']
        total_field = config['total_field']

        # 构建查询条件
        query_conditions = [
            model.result == "成功",
            model.delete == 0
        ]

        if mode == BLOCK_MODE:
            query_conditions.append(model.block_script == 1)
        else:  # IDENTIFY_MODE
            query_conditions.append(model.identify_script == 1)

        # 查询数据
        items = model.query.filter(and_(*query_conditions)).all()

        # 更新任务统计
        setattr(task, total_field, len(items))

        # 创建TaskRecord
        for item in items:
            task_record = TaskRecord(appid=item.id, platform=platform_id)
            task.task_record.append(task_record)

    def _process_file_task(self, file: FileStorage, mode: str, task: Task) -> bool:
        """处理文件任务"""
        try:
            # 保存文件
            file_path = self._save_uploaded_file(file)
            if not file_path:
                return False

            # 读取任务文件
            data = read_task_file(file_path)
            if not self._has_valid_data(data):
                return True

            # 处理各平台数据
            self._process_file_platforms(data, mode, task)

            return True

        except Exception as e:
            logging.error(f"处理文件任务失败: {str(e)}")
            return False

    def _save_uploaded_file(self, file: FileStorage) -> Optional[str]:
        """保存上传的文件"""
        try:
            filename = f"{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}-" + file.filename
            path = os.path.join(Config.BASEDIR, 'tmp', filename)
            file.save(path)
            return path
        except Exception as e:
            logging.error(f"保存文件失败: {str(e)}")
            return None

    def _has_valid_data(self, data: Dict[str, List[str]]) -> bool:
        """检查是否有有效数据"""
        return any(data[platform_key] for platform_key in PLATFORM_CONFIG.keys())

    def _process_file_platforms(self, data: Dict[str, List[str]], mode: str, task: Task) -> None:
        """处理文件中的各平台数据"""
        for platform_key, config in PLATFORM_CONFIG.items():
            if data[platform_key]:
                self._process_file_platform_data(
                    data[platform_key], platform_key, mode, task, config
                )

    def _process_file_platform_data(self, l_class_list: List[str], mode: str,
                                   task: Task, config: Dict[str, Any]) -> None:
        """处理单个平台的文件数据"""
        model = config['model']
        platform_id = config['platform_id']
        total_field = config['total_field']

        current_total = getattr(task, total_field)

        for l_class in l_class_list:
            # 构建查询条件
            query_conditions = [model.l_class == l_class]
            if mode == BLOCK_MODE:
                query_conditions.append(model.block_script == 1)
            else:  # IDENTIFY_MODE
                query_conditions.append(model.identify_script == 1)

            # 查询记录
            item = model.query.filter(and_(*query_conditions)).first()
            if item:
                current_total += 1
                task_record = TaskRecord(appid=item.id, platform=platform_id)
                task.task_record.append(task_record)

        # 更新统计
        setattr(task, total_field, current_total)
