# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   inspection_enhanced.py
@Time    :   2024/4/11 13:18
"""
import os
import datetime
import logging
from typing import Dict, List, Optional, Any
from flask_restful import Resource, reqparse
from werkzeug.datastructures import FileStorage
from sqlalchemy import and_
from app import db
from app.models import CnAndroid, EnAndroid, CnIOS, EnIOS, Task, TaskRecord
from app.resources.user import authenticate
from app.utils import read_task_file
from config import Config



class InspectionEnhanced(Resource):
    method_decorators = [authenticate]

    @staticmethod
    def _parse_arguments() -> Dict[str, Any]:
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, location="form")
        parser.add_argument("platform", type=str, location="form")
        parser.add_argument("mode", type=str, location="form")
        parser.add_argument('file', type=FileStorage, location='files')
        args = parser.parse_args()

        return {
            'name': args.get("name"),
            'platforms': args.get("platform", ""),
            'mode': args.get("mode", ""),
            'file': args.get('file')
        }

    def post(self) -> Dict[str, Any]:
        try:
            args = self._parse_arguments()

            validation_result = self._validate_parameters(args)
            if validation_result:
                return validation_result

            task = self._create_task(args['name'], args['mode'])

            if args['platforms']:
                self._process_platform_tasks(args, task)

            if args['file']:
                file_result = self._process_file_task(args['file'], args['mode'], task)
                if not file_result:
                    db.session.rollback()
                    return {"code": 400, "message": "任务文件异常"}

            db.session.commit()
            return {"code": 200, "message": "新增日常巡检成功！"}

        except Exception as e:
            db.session.rollback()
            logging.error(f"创建巡检任务失败: {str(e)}")
            return {"code": 500, "message": "数据库操作异常"}



    @staticmethod
    def _validate_parameters(args: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        if not args['name'] or (not args['platforms'] and not args['file']):
            return {"code": 400, "message": "参数错误"}

        if args['mode'] not in ['block', 'identify']:
            return {"code": 400, "message": "mode参数必须是block或identify"}

        existing_task = Task.query.filter_by(name=args['name']).first()
        if existing_task:
            return {"code": 400, "message": "任务名称重复！"}

        return None

    @staticmethod
    def _create_task(name: str, mode: str) -> Task:
        task = Task(name=name, mode=mode)
        db.session.add(task)
        db.session.flush()
        return task

    def _process_platform_tasks(self, args: Dict[str, Any], task: Task) -> None:
        platforms = args['platforms'].split("+")
        for platform_key in platforms:
            if platform_key == Config.CN_ANDROID:
                self._process_cn_android(args['mode'], task)
            elif platform_key == Config.EN_ANDROID:
                self._process_en_android(args['mode'], task)
            elif platform_key == Config.CN_IOS:
                self._process_cn_ios(args['mode'], task)
            elif platform_key == Config.EN_IOS:
                self._process_en_ios(args['mode'], task)

    @staticmethod
    def _process_cn_android(mode: str, task: Task) -> None:
        if mode == 'block':
            items = CnAndroid.query.filter(and_(CnAndroid.result == "成功", CnAndroid.delete == 0, CnAndroid.block_script == 1)).all()
        else:
            items = CnAndroid.query.filter(and_(CnAndroid.result == "成功", CnAndroid.delete == 0, CnAndroid.identify_script == 1)).all()

        task.cn_android_total = len(items)
        for item in items:
            task_record = TaskRecord(appid=item.id, platform=1)
            task.task_record.append(task_record)

    @staticmethod
    def _process_en_android(mode: str, task: Task) -> None:
        if mode == 'block':
            items = EnAndroid.query.filter(and_(EnAndroid.result == "成功", EnAndroid.delete == 0, EnAndroid.block_script == 1)).all()
        else:
            items = EnAndroid.query.filter(and_(EnAndroid.result == "成功", EnAndroid.delete == 0, EnAndroid.identify_script == 1)).all()

        task.en_android_total = len(items)
        for item in items:
            task_record = TaskRecord(appid=item.id, platform=2)
            task.task_record.append(task_record)

    @staticmethod
    def _process_cn_ios(mode: str, task: Task) -> None:
        if mode == 'block':
            items = CnIOS.query.filter(and_(CnIOS.result == "成功", CnIOS.delete == 0, CnIOS.block_script == 1)).all()
        else:
            items = CnIOS.query.filter(and_(CnIOS.result == "成功", CnIOS.delete == 0, CnIOS.identify_script == 1)).all()

        task.cn_ios_total = len(items)
        for item in items:
            task_record = TaskRecord(appid=item.id, platform=3)
            task.task_record.append(task_record)

    @staticmethod
    def _process_en_ios(mode: str, task: Task) -> None:
        if mode == 'block':
            items = EnIOS.query.filter(and_(EnIOS.result == "成功", EnIOS.delete == 0, EnIOS.block_script == 1)).all()
        else:
            items = EnIOS.query.filter(and_(EnIOS.result == "成功", EnIOS.delete == 0, EnIOS.identify_script == 1)).all()

        task.en_ios_total = len(items)
        for item in items:
            task_record = TaskRecord(appid=item.id, platform=4)
            task.task_record.append(task_record)

    def _process_file_task(self, file: FileStorage, mode: str, task: Task) -> bool:
        try:
            file_path = self._save_uploaded_file(file)
            if not file_path:
                return False
            data = read_task_file(file_path)
            if not self._has_valid_data(data):
                return True

            self._process_file_platforms(data, mode, task)

            return True

        except Exception as e:
            logging.error(f"处理文件任务失败: {str(e)}")
            return False

    @staticmethod
    def _save_uploaded_file(file: FileStorage) -> Optional[str]:
        try:
            filename = f"{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}-" + file.filename
            path = os.path.join(Config.BASEDIR, 'tmp', filename)
            file.save(path)
            return path
        except Exception as e:
            logging.error(f"保存文件失败: {str(e)}")
            return None

    @staticmethod
    def _has_valid_data(data: Dict[str, List[str]]) -> List[str]:
        return (data[Config.CN_ANDROID] or data[Config.EN_ANDROID] or
                data[Config.CN_IOS] or data[Config.EN_IOS])

    def _process_file_platforms(self, data: Dict[str, List[str]], mode: str, task: Task) -> None:
        if data[Config.CN_ANDROID]:
            self._process_file_cn_android(data[Config.CN_ANDROID], mode, task)

        if data[Config.EN_ANDROID]:
            self._process_file_en_android(data[Config.EN_ANDROID], mode, task)

        if data[Config.CN_IOS]:
            self._process_file_cn_ios(data[Config.CN_IOS], mode, task)

        if data[Config.EN_IOS]:
            self._process_file_en_ios(data[Config.EN_IOS], mode, task)

    @staticmethod
    def _process_file_cn_android(l_class_list: List[str], mode: str, task: Task) -> None:
        current_total = task.cn_android_total
        for l_class in l_class_list:
            if mode == 'block':
                item = CnAndroid.query.filter(and_(CnAndroid.l_class == l_class, CnAndroid.block_script == 1)).first()
            else:
                item = CnAndroid.query.filter(and_(CnAndroid.l_class == l_class, CnAndroid.identify_script == 1)).first()

            if item:
                current_total += 1
                task_record = TaskRecord(appid=item.id, platform=1)
                task.task_record.append(task_record)

        task.cn_android_total = current_total

    @staticmethod
    def _process_file_en_android(l_class_list: List[str], mode: str, task: Task) -> None:
        current_total = task.en_android_total
        for l_class in l_class_list:
            if mode == 'block':
                item = EnAndroid.query.filter(and_(EnAndroid.l_class == l_class, EnAndroid.block_script == 1)).first()
            else:
                item = EnAndroid.query.filter(and_(EnAndroid.l_class == l_class, EnAndroid.identify_script == 1)).first()

            if item:
                current_total += 1
                task_record = TaskRecord(appid=item.id, platform=2)
                task.task_record.append(task_record)

        task.en_android_total = current_total

    @staticmethod
    def _process_file_cn_ios(l_class_list: List[str], mode: str, task: Task) -> None:
        current_total = task.cn_ios_total
        for l_class in l_class_list:
            if mode == 'block':
                item = CnIOS.query.filter(and_(CnIOS.l_class == l_class, CnIOS.block_script == 1)).first()
            else:
                item = CnIOS.query.filter(and_(CnIOS.l_class == l_class, CnIOS.identify_script == 1)).first()

            if item:
                current_total += 1
                task_record = TaskRecord(appid=item.id, platform=3)
                task.task_record.append(task_record)

        task.cn_ios_total = current_total

    @staticmethod
    def _process_file_en_ios(l_class_list: List[str], mode: str, task: Task) -> None:
        current_total = task.en_ios_total
        for l_class in l_class_list:
            if mode == 'block':
                item = EnIOS.query.filter(and_(EnIOS.l_class == l_class, EnIOS.block_script == 1)).first()
            else:
                item = EnIOS.query.filter(and_(EnIOS.l_class == l_class, EnIOS.identify_script == 1)).first()

            if item:
                current_total += 1
                task_record = TaskRecord(appid=item.id, platform=4)
                task.task_record.append(task_record)

        task.en_ios_total = current_total
