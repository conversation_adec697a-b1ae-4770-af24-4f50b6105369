<template>
	<div>
		<div class="container">
			<el-button
				v-if="author_permiss"
				type="primary"
				@click="addInspection1"
				>
				新增任务
			</el-button>
			<el-divider direction="vertical"/>
			<el-link :underline="true" type="primary" :href="tmpUrl">任务模板</el-link>
			<el-divider direction="vertical" />
			<el-button
				v-if="author_permiss"
				type="success"
				@click="refresh1"
				>
				重新下发
			</el-button>
			<!-- <div class="content-title">任务列表</div> -->
			<el-divider class="content-title">任务列表</el-divider>
			<el-table v-loading="loading" :data="tableData1" border class="table" ref="multipleTable" header-cell-class-name="table-header">
				<el-table-column prop="id" label="ID" width="60" align="center"></el-table-column>
				<el-table-column prop="created_at" label="创建时间" width="140" align="center"></el-table-column>
				<el-table-column label="任务名" width="140" align="center">
					<template #default="scope">
						<el-button
							type="primary"
							link
							@click="handleEdit(scope.row)"
						>
							{{ scope.row.name }}
						</el-button>
					</template>
				</el-table-column>
				<el-table-column prop="cn_android_total" label="国内安卓" align="center"></el-table-column>
				<el-table-column prop="en_android_total" label="国外安卓" align="center"></el-table-column>
				<el-table-column prop="cn_ios_total" label="国内IOS" width="80" align="center"></el-table-column>
				<el-table-column prop="en_ios_total" label="国外IOS" width="80" align="center"></el-table-column>
				<el-table-column prop="mode" label="类型" width="80" align="center"></el-table-column>
				<el-table-column prop="success" label="成功" align="center"></el-table-column>
				<el-table-column prop="failed" label="失败" align="center"></el-table-column>
				<el-table-column prop="progress" label="进度" align="center"></el-table-column>
				<el-table-column prop="status" label="状态" width="80" align="center"></el-table-column>
				<el-table-column label="未修复" width="80" align="center">
					<template #default="scope">
						<el-button
							type="primary"
							link
							@click="handleStat(scope.row)"
						>
							{{ scope.row.unrepaired }}
						</el-button>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="160" align="center">
					<template #default="scope">
						<el-button
							type="primary"
							link
							v-if="author_permiss && (scope.row.status_number==0||scope.row.status_number==2)"
							@click="handleStart(scope.row)"
						>
							开始
						</el-button>
						<el-button
							type="primary"
							link
							v-else-if="author_permiss && scope.row.status_number==3"
							@click="handlePause(scope.row)"
						>
							暂停
						</el-button>
						<el-button
							type="info"
							link
							v-else
						>
							------
						</el-button>
						<el-button
							type="success"
							link
							@click="handleRefresh(scope.row)"
						>
							刷新
						</el-button>
						<el-button
							v-if="author_permiss"
							type="warning"
							link
							@click="handleDelete(scope.row)"
						>
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div class="pagination">
				<el-pagination
					background
					layout="total, prev, pager, next"
					:current-page="query1.pageIndex"
					:page-size="query1.pageSize"
					:total="pageTotal1"
					@current-change="handlePageChange1"
					>
				</el-pagination>
			</div>
		</div>

		<el-dialog
			v-model="taskVisible"
			title="新增任务"
			width="600px"
			align-center
			>
			<el-form :model="taskForm" label-width="auto">
				<el-form-item label="任务名称">
				<el-input v-model="taskForm.name" />
				</el-form-item>
				
				<!-- 新增：识别/阻断单选框 -->
				<el-form-item label="操作模式">
					<el-radio-group v-model="taskForm.mode">
						<el-radio label="block">阻断</el-radio>
						<el-radio label="identify">识别</el-radio>
					</el-radio-group>
				</el-form-item>
				
				<el-form-item label="平台类型">
				<el-checkbox-group v-model="taskForm.type">
					<el-checkbox value="cn_android" name="type">
					国内Android
					</el-checkbox>
					<el-checkbox value="en_android" name="type">
					国外Android
					</el-checkbox>
					<el-checkbox value="cn_ios" name="type">
					国内IOS
					</el-checkbox>
					<el-checkbox value="en_ios" name="type">
					国外IOS
					</el-checkbox>
				</el-checkbox-group>
				</el-form-item>
				
				<el-form-item label="选择模板">
				<el-upload
					ref="uploadRef"
					:limit="1"
					:auto-upload="false"
					class="avatar-uploader"
					accept=".csv"
					action="#"
					:on-exceed="exceedFile"
					:on-error="handleError"
					:on-success="handleSuccess"
					:show-file-list="true"
					:before-upload="beforeUPload"
					v-model:file-list="taskForm.fileList"
				>
					<el-button type="primary">选择文件</el-button>
				</el-upload>
				</el-form-item>
			</el-form>
			
			<template #footer>
				<div class="dialog-footer">
				<el-button type="primary" @click="onSubmit">提交</el-button>
				<el-button @click="taskVisible = false">关闭</el-button>
				</div>
			</template>
		</el-dialog>

		<el-dialog
			v-model="detailVisible"
			title="任务详情"
			width="1000px"
			align-center
			>
			<el-row>
				<el-col :span="6">
					<el-select
						v-model="query2.platform"
						placeholder="平台类型"
						clearable
						class="mr10"
						>
						<el-option
							v-for="item in platform_options"
								:key="item.value"
								:label="item.label"
								:value="item.value"
						/>
					</el-select>
				</el-col>
				<el-col :span="6">
					<el-select
						v-model="query2.result"
						placeholder="结果"
						clearable
						class="mrl10"
						>
						<el-option
							v-for="item in result_options"
								:key="item.value"
								:label="item.label"
								:value="item.value"
						/>
					</el-select>
				</el-col>
				<el-col :span="6">
					<el-select
						v-model="query2.repaired"
						placeholder="是否修复"
						clearable
						class="mrl20"
						>
						<el-option
							v-for="item in repaired_options"
								:key="item.value"
								:label="item.label"
								:value="item.value"
						/>
					</el-select>
				</el-col>
				<el-col :span="6">
					<el-button type="primary" :icon="Search" class="mrl30" @click="searchTaskDetail">搜索</el-button>
					<el-button type="warning" :icon="Download" class="mrl30" @click="download">下载</el-button>
				</el-col>
			</el-row>
			<el-divider>任务详情</el-divider>
			<el-table :data="tableData2" border class="table" ref="multipleTable" header-cell-class-name="table-header">
				<el-table-column prop="id" label="ID" width="100" align="center"></el-table-column>
				<el-table-column prop="l_class" label="小类ID" align="center"></el-table-column>
				<el-table-column prop="platform" label="平台类型" align="center"></el-table-column>
				<el-table-column prop="result" label="结果" align="center"></el-table-column>
				<el-table-column prop="repaired" label="是否修复" align="center"></el-table-column>
				<el-table-column prop="author" label="责任人" align="center"></el-table-column>
			</el-table>
			<div class="pagination">
				<el-pagination
					background
					layout="total, prev, pager, next"
					:current-page="query2.pageIndex"
					:page-size="query2.pageSize"
					:total="pageTotal2"
					@current-change="handlePageChange2"
					>
				</el-pagination>
			</div>
			<el-divider>华丽的分割线</el-divider>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="detailVisible = false">关闭</el-button>
				</div>
			</template>
		</el-dialog>

		<el-dialog
			v-model="statVisible"
			title="修复统计"
			width="600px"
			align-center
			>
			<el-divider>失败未修复统计</el-divider>
			<el-table :data="tableData3" border class="table" ref="multipleTable" header-cell-class-name="table-header">
				<el-table-column prop="cn_android" label="Android/cn" align="center"></el-table-column>
				<el-table-column prop="en_android" label="Android/en" align="center"></el-table-column>
				<el-table-column prop="cn_ios" label="IOS/cn" align="center"></el-table-column>
				<el-table-column prop="en_ios" label="IOS/en" align="center"></el-table-column>
				<el-table-column prop="author" label="责任人" align="center"></el-table-column>
			</el-table>
			<el-divider>华丽的分割线</el-divider>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="statVisible = false">关闭</el-button>
				</div>
			</template>
		</el-dialog>

		<el-dialog
			v-model="delDialogVisible"
			title="警告"
			width="400"
			align-center
			>
			<span>确定要删除 {{ delRow.name }} 任务吗？</span>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="danger" @click="handleDel">
					删除
					</el-button>
					<el-button @click="delDialogVisible = false">取消</el-button>
				</div>
			</template>
		</el-dialog>

		<el-dialog
				title="表格下载"
				v-model="visibleApp"
				width="200px"
				destroy-on-close
				:close-on-click-modal="false"
				>
				<el-text v-if="visibleTip">{{ tipMsg }}</el-text>
				<el-link v-if="visibleLink" :underline="true" type="primary" :href="fileLink">点击下载</el-link>
				<el-progress :percentage="appProgress" />
				<template #footer>
					<div class="dialog-footer">
						<el-button @click="visibleApp = false">关闭</el-button>
					</div>
				</template>
		</el-dialog>

	</div>
</template>

<script setup lang="ts" name="inspection">
import { ref, reactive } from 'vue';
import { Search, Download } from '@element-plus/icons-vue';
import { ElMessage, UploadInstance, ElMessageBox } from 'element-plus';
import { addInspection, queryInspection, refreshInspection, taskStatus, refreshTask, taskDetail, statDetail, taskDelete, taskLink, downloadTaskDetail } from '../api/index';
import { usePermissStore } from '../store/permiss';

const permiss = usePermissStore();
const author_permiss = permiss.key.indexOf('4') == -1 ? false : true; 
const tmpUrl = ref("");

const uploadRef = ref<UploadInstance>();

// 国内Android
interface TableItem1 {
	id: number;
	created_at: Date;
	name: string;
	cn_android_total: number;
	en_android_total: number;
	cn_ios_total: number;
	en_ios_total: number;
	success: string;
	failed: string;
	progress: string;
	status: string;
	status_number: number;
	unrepaired: number;
}

const tableData1 = ref<TableItem1[]>([]);
const pageTotal1 = ref(0);

const query1 = reactive({
	pageIndex: 1,
	pageSize: 10
});

const queryIns1 = async () => {
	try {
		const rsp = await queryInspection(query1);
		if (rsp.data.code == 200) {
			tableData1.value = rsp.data.list;
			pageTotal1.value = rsp.data.total;
		}
	} catch (err) {
		ElMessage.error("服务器异常！")
	}
}

const handlePageChange1 = (val: number) => {
	query1.pageIndex = val;
	queryIns1();
};

queryIns1();



// 开始
const taskVisible = ref(false);
const loading = ref(false);
const taskForm = reactive({
  name: '',
  type: [],
  fileList: [],
  mode:"block"
})


const taskOver = async () => {
	taskVisible.value = true;
}


// 新增巡检
const addInspection1 = () => {
	taskOver();
}

// 文件数超出提示
const exceedFile = () => {
	ElMessage.warning('最多只能上传一个文件！');
};
// 上传错误提示
const handleError = () => {
	ElMessage.error('导入数据失败，请您重新上传！');
}; 
//上传成功提示
const handleSuccess = () => {
	ElMessage.success('导入数据成功！');
};

const beforeUPload = (file: any) => {
	const fileType = file.type.substring(file.type.lastIndexOf("/") + 1, file.type.length);
	let isExcel = (fileType == 'csv');
	if (!isExcel)
		ElMessageBox({
			title: '温馨提示',
			message: '上传文件只能是 csv 格式！',
			type: 'warning',
		});
	return isExcel;
};

const addInspect = async () => {
	if (taskForm.fileList.length != 0) {
		let gasDataFile = taskForm.fileList[0].raw

		if (!beforeUPload(gasDataFile)) {
			return
		}
	}
	try {
		loading.value = true;
		taskVisible.value = false;
		const rsp = await addInspection(taskForm);
		if (rsp.data.code == 200) {
			ElMessage.success(rsp.data.message);
			queryIns1();
			taskForm.name = "";
			taskForm.fileList = [];
			taskForm.type = [];
		} else {
			ElMessage.error(rsp.data.message);
		}
	} catch (err) {
		ElMessage.error("服务器异常！")
	}
	loading.value = false;
}

const onSubmit = () => {
	addInspect();
}

const handleRefresh = async (row: TableItem1) => {
	try {
		loading.value = true;
		const rsp = await refreshTask(row.id);
		if (rsp.data.code == 200) {
			ElMessage.success(rsp.data.message);
			queryIns1();
		} else {
			ElMessage.error(rsp.data.message);
		}
	} catch (err) {
		ElMessage.error("服务器异常！")
	}
	loading.value = false;
}

// 详情
const detailVisible = ref(false);
const statVisible = ref(false);
const pageTotal2 = ref(0);

const platform_options = [
	{
    	value: 1,
    	label: '国内Android',
  	},
	{
		value: 2,
		label: '国外Android',
	},
	{
		value: 3,
		label: '国内IOS',
	},
	{
		value: 4,
		label: '国外IOS',
	}
]

const result_options = [
	{
    	value: 0,
    	label: '未出结果',
  	},
	{
		value: 1,
		label: '成功',
	},
	{
		value: 2,
		label: '失败',
	}
]

const repaired_options = [
	{
    	value: 0,
    	label: '否',
  	},
	{
		value: 1,
		label: '是',
	}
]

interface TableItem2 {
	id: number;
	l_class: string;
	platform: string;
	result: string;
	repaired: string;
	author: string;
}

const tableData2 = ref<TableItem2[]>([]);


interface TableItem3 {
	cn_android: number;
	en_android: number;
	cn_ios: number;
	en_ios: number;
	author: string;
}

const tableData3 = ref<TableItem3[]>([]);


const query2 = reactive({
	taskId: 0,
	platform: '',
	result: '',
	repaired: '',
	pageIndex: 1,
	pageSize: 10
});

const queryTaskDetail = async () => {
	try {
		const rsp = await taskDetail(query2);
		if (rsp.data.code == 200) {
			tableData2.value = rsp.data.list;
			pageTotal2.value = rsp.data.total;
			detailVisible.value = true;
		} else {
			ElMessage.error("服务器异常！")
		}
	} catch (err) {
		ElMessage.error("服务器异常！")
	}
}

// 任务详情下载
const visibleApp = ref(false);
const tipMsg = ref("");
const appProgress = ref(0);
const visibleTip = ref(false);
const visibleLink = ref(false);
const fileLink = ref("#");

const queryDownloadTaskDetail = async () => {
	visibleApp.value = true;
	visibleTip.value = true;
	visibleLink.value = false;
	fileLink.value = "#";
	tipMsg.value = "表格生成中...";
	appProgress.value = Math.floor(Math.random() * 100);
	try {
		const rsp = await downloadTaskDetail(query2);
		if (rsp.data.code == 200) {
			visibleTip.value = false;
			appProgress.value = 100;
			visibleLink.value = true;
			fileLink.value = rsp.data.link;
		} else {
			tipMsg.value = "表格生成失败，请重试！"
		}
	} catch (err) {
		ElMessage.error('表格下载异常！');
	}
}


const handleEdit = (row: TableItem1) => {
	query2.taskId = row.id;
	query2.pageIndex = 1;
	query2.platform = '';
	query2.repaired = '';
	query2.result = '';
	queryTaskDetail();
};

const handlePageChange2 = (val: number) => {
	query2.pageIndex = val;
	queryTaskDetail();
};

const searchTaskDetail = () => {
	query2.pageIndex = 1;
	queryTaskDetail();
}

const download = () => {
	queryDownloadTaskDetail();
}

const queryStatTask = async (taskId: number) => {
	try {
		const rsp = await statDetail(taskId);
		if (rsp.data.code == 200) {
			tableData3.value = rsp.data.list;
			statVisible.value = true;
		} else {
			ElMessage.error("服务器异常！")
		}
	} catch (err) {
		ElMessage.error("服务器异常！")
	}
}


const taskStatusUpdate = async (taskId: number, statusNumber: number) => {
	try {
		const rsp = await taskStatus(taskId, statusNumber)
		if (rsp.data.code == 200) {
			ElMessage.success(rsp.data.message);
			queryIns1();
		} else {
			ElMessage.error(rsp.data.message)
		}
	} catch (err) {
		ElMessage.error("服务器异常！")
	}
}


const handleStat = (row: TableItem1) => {
	queryStatTask(row.id)
}


const handleStart = (row: TableItem1) => {
	taskStatusUpdate(row.id, 3)
}


const handlePause = (row: TableItem1) => {
	taskStatusUpdate(row.id, 2)
}


// 日常巡检刷新
const refresh1 = async () => {
	try {
		const rsp = await refreshInspection("All");
		if (rsp.data.code == 200) {
			ElMessage.success(rsp.data.message);
		} else {
			ElMessage.error(rsp.data.message);
		}
	} catch (err) {
		ElMessage.error("服务器异常！")
	}
}


// 删除
const delDialogVisible = ref(false);
const delRow = ref({
	taskId: -1,
	name: ""
});

const handleDelete = (row: TableItem1) => {
	delDialogVisible.value = true;
	delRow.value.taskId = row.id;
	delRow.value.name = row.name;
}


const handleDel = async () => {
	delDialogVisible.value = false;
	try {
		const rsp = await taskDelete(delRow.value.taskId)
		if (rsp.data.code == 200){
			ElMessage.success(rsp.data.message);
			queryIns1();
		} else {
			ElMessage.error(rsp.data.message);
		}
	} catch (err) {
		ElMessage.error("服务器异常！")
	}
}


const getTaskLink = async () => {
	try {
		const rsp = await taskLink();
		if (rsp.data.code == 200) {
			tmpUrl.value = rsp.data.message
		} else {
			ElMessage.error("获取任务模板异常！")
		}
	} catch (err) {
		ElMessage.error("服务器异常！")
	}
}

getTaskLink();

</script>

<style scoped>
.search-box {
	margin-bottom: 20px;
}

.mr10 {
	margin-right: 10px;
}

.mrl10 {
	margin-left: 10px;
}

.mrl20 {
	margin-left: 20px;
}

.mrl30 {
	margin-left: 30px;
}

.tree-wrapper {
	max-width: 500px;
}

.label {
	font-size: 14px;
}
.content-title {
	clear: both;
	font-weight: 400;
	line-height: 50px;
	font-size: 22px;
	color: #1f2f3d;
}

.mgb20 {
	margin-bottom: 20px;
}

.avatar-uploader .el-upload {
	border: 1px dashed var(--el-border-color);
	border-radius: 6px;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
	border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 178px;
	height: 178px;
	text-align: center;
}

</style>
