# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   models.py
@Time    :   2024/3/26 17:23
"""
import datetime

from passlib.apps import custom_app_context as pwd_context
from sqlalchemy.schema import UniqueConstraint
from authlib.jose import jwt, Jose<PERSON><PERSON><PERSON>
from app import db, record_script_app


class User(db.Model):
    __tablename__ = 'user'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), index=True, unique=True)
    password_hash = db.Column(db.String(128))
    # 是否激活（0： 未激活，1：激活）
    activity = db.Column(db.Integer, default=1)
    role_id = db.Column(db.Integer, db.<PERSON><PERSON>('role.id'))
    cn_android = db.relationship("CnAndroid", backref='user', lazy='dynamic')
    en_android = db.relationship("EnAndroid", backref='user', lazy='dynamic')
    cn_ios = db.relationship("CnIOS", backref='user', lazy='dynamic')
    en_ios = db.relationship("EnIOS", backref='user', lazy='dynamic')

    def __repr__(self):
        return f'<User {self.username}>'

    def hash_password(self, password: str):
        self.password_hash = pwd_context.encrypt(password)

    def verify_password(self, password: str):
        return pwd_context.verify(password, self.password_hash)

    def generate_auth_token(self):
        header = {'alg': "HS256"}
        key = record_script_app.config["SECRET_KEY"]
        data = {'id': self.id}
        return jwt.encode(header=header, payload=data, key=key)

    @staticmethod
    def verify_auth_token(token):
        key = record_script_app.config["SECRET_KEY"]
        try:
            data = jwt.decode(token, key)
        except JoseError:
            return
        user = User.query.get(data['id'])
        return user


# 角色-权限多对多关系
role_permission = db.Table('role_permission',
                           db.Column('role_id', db.Integer, db.ForeignKey('role.id')),
                           db.Column('permission_id', db.Integer, db.ForeignKey('permission.id'))
                           )


class Role(db.Model):
    __tablename__ = 'role'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80), unique=True)
    users = db.relationship('User', backref='role', lazy='dynamic')
    permissions = db.relationship('Permission', secondary=role_permission, backref='roles')


class Permission(db.Model):
    __tablename__ = 'permission'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(50), nullable=False)
    desc = db.Column(db.String(100))
    type = db.Column(db.String(20))      
    parent_id = db.Column(db.Integer)    

class CnAndroid(db.Model):
    __tablename__ = 'cn_android'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80))
    l_class = db.Column(db.Integer, unique=True)
    package = db.Column(db.String(60))
    # 优先级（1：低，2：中，3：高）
    priority = db.Column(db.Integer)
    result = db.Column(db.String(32), default="未出结果")
    remark = db.Column(db.String(128))
    # 巡检标记（0：未标记为巡检， 1：标记为巡检）
    inspect_flag = db.Column(db.Integer, default=0)
    # 日常巡检结果（0：默认值，1：成功，2：失败）
    inspection = db.Column(db.Integer, default=0)
    # 是否是重要应用（0：否，1：是）
    important = db.Column(db.Integer, default=0)
    # 来自应用商店
    store = db.Column(db.String(32), default="")
    # 是否删除（0：否，1：是）
    delete = db.Column(db.Integer, default=0)
    # 原因分析
    reason = db.Column(db.String(128))
    # 原作者
    author_id = db.Column(db.Integer)
    # 任务ID
    task_id = db.Column(db.Integer)
    # 是否阻断脚本（0：不阻断，1：阻断脚本）
    block_script = db.Column(db.Integer, default=0)
    # 是否识别为脚本（0：不是识别脚本，1：是识别脚本）
    identify_script = db.Column(db.Integer, default=0)
    # 脚本执行状态（0：未执行，1：执行中）
    identify_flag = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=db.func.now())
    updated_at = db.Column(db.DateTime, default=db.func.now(), onupdate=db.func.now())
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    cn_android_record = db.relationship("CnAndroidRecord", backref='cn_android', lazy='dynamic')

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def updated_at_aware(self):
        return self.updated_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class EnAndroid(db.Model):
    __tablename__ = 'en_android'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80))
    l_class = db.Column(db.Integer, unique=True)
    package = db.Column(db.String(60))
    # 优先级（1：低，2：中，3：高）
    priority = db.Column(db.Integer)
    result = db.Column(db.String(32), default="未出结果")
    remark = db.Column(db.String(128))
    # 巡检标记（0：未标记为巡检， 1：标记为巡检）
    inspect_flag = db.Column(db.Integer, default=0)
    # 日常巡检结果（0：默认值，1：成功，2：失败）
    inspection = db.Column(db.Integer, default=0)
    # 是否是重要应用（0：否，1：是）
    important = db.Column(db.Integer, default=0)
    # 来自应用商店
    store = db.Column(db.String(32), default="")
    # 是否删除（0：否，1：是）
    delete = db.Column(db.Integer, default=0)
    # 原因分析
    reason = db.Column(db.String(128))
    # 原作者
    author_id = db.Column(db.Integer)
    # 任务ID
    task_id = db.Column(db.Integer)
    # 是否阻断脚本（0：不阻断，1：阻断脚本）
    block_script = db.Column(db.Integer, default=0)
    # 是否识别为脚本（0：不是识别脚本，1：是识别脚本）
    identify_script = db.Column(db.Integer, default=0)
    # 脚本执行状态（0：未执行，1：执行中）
    identify_flag = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=db.func.now())
    updated_at = db.Column(db.DateTime, default=db.func.now(), onupdate=db.func.now())
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    en_android_record = db.relationship("EnAndroidRecord", backref='en_android', lazy='dynamic')

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def updated_at_aware(self):
        return self.updated_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class CnIOS(db.Model):
    __tablename__ = 'cn_ios'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80))
    l_class = db.Column(db.Integer, unique=True)
    package = db.Column(db.String(60))
    # 优先级（1：低，2：中，3：高）
    priority = db.Column(db.Integer)
    result = db.Column(db.String(32), default="未出结果")
    remark = db.Column(db.String(128))
    # 巡检标记（0：未标记为巡检， 1：标记为巡检）
    inspect_flag = db.Column(db.Integer, default=0)
    # 日常巡检结果（0：默认值，1：成功，2：失败）
    inspection = db.Column(db.Integer, default=0)
    # 是否是重要应用（0：否，1：是）
    important = db.Column(db.Integer, default=0)
    # 来自应用商店
    store = db.Column(db.String(32), default="")
    # 是否删除（0：否，1：是）
    delete = db.Column(db.Integer, default=0)
    # 原因分析
    reason = db.Column(db.String(128))
    # 原作者
    author_id = db.Column(db.Integer)
    # 任务ID
    task_id = db.Column(db.Integer)
    # 是否阻断脚本（0：不阻断，1：阻断脚本）
    block_script = db.Column(db.Integer, default=0)
    # 是否识别为脚本（0：不是识别脚本，1：是识别脚本）
    identify_script = db.Column(db.Integer, default=0)
    # 脚本执行状态（0：未执行，1：执行中）
    identify_flag = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=db.func.now())
    updated_at = db.Column(db.DateTime, default=db.func.now(), onupdate=db.func.now())
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    cn_ios_record = db.relationship("CnIOSRecord", backref='cn_ios', lazy='dynamic')

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def updated_at_aware(self):
        return self.updated_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class EnIOS(db.Model):
    __tablename__ = 'en_ios'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80))
    l_class = db.Column(db.Integer, unique=True)
    package = db.Column(db.String(60))
    # 优先级（1：低，2：中，3：高）
    priority = db.Column(db.Integer)
    result = db.Column(db.String(32), default="未出结果")
    remark = db.Column(db.String(128))
    # 巡检标记（0：未标记为巡检， 1：标记为巡检）
    inspect_flag = db.Column(db.Integer, default=0)
    # 日常巡检结果（0：默认值，1：成功，2：失败）
    inspection = db.Column(db.Integer, default=0)
    # 是否是重要应用（0：否，1：是）
    important = db.Column(db.Integer, default=0)
    # 来自应用商店
    store = db.Column(db.String(32), default="")
    # 是否删除（0：否，1：是）
    delete = db.Column(db.Integer, default=0)
    # 原因分析
    reason = db.Column(db.String(128))
    # 原作者
    author_id = db.Column(db.Integer)
    # 任务ID
    task_id = db.Column(db.Integer)
    # 是否阻断脚本（0：不阻断，1：阻断脚本）
    block_script = db.Column(db.Integer, default=0)
    # 是否识别为脚本（0：不是识别脚本，1：是识别脚本）
    identify_script = db.Column(db.Integer, default=0)
    # 脚本执行状态（0：未执行，1：执行中）
    identify_flag = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=db.func.now())
    updated_at = db.Column(db.DateTime, default=db.func.now(), onupdate=db.func.now())
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    en_ios_record = db.relationship("EnIOSRecord", backref='en_ios', lazy='dynamic')

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def updated_at_aware(self):
        return self.updated_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class InspectionRecord(db.Model):
    __tablename__ = 'inspection_record'
    id = db.Column(db.Integer, primary_key=True)
    script_type = db.Column(db.String(80))
    created_at = db.Column(db.DateTime, default=db.func.now())
    count = db.Column(db.Integer, default=0)
    success = db.Column(db.Integer, default=0)
    failed = db.Column(db.Integer, default=0)
    over = db.Column(db.Integer, default=0)

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class CnAndroidRecord(db.Model):
    __tablename__ = 'cn_android_record'
    id = db.Column(db.Integer, primary_key=True)
    l_class = db.Column(db.Integer)
    mobile_ip = db.Column(db.String(32))
    router_host = db.Column(db.String(32))
    router_port = db.Column(db.Integer)
    extend_device = db.Column(db.String(32))
    created_at = db.Column(db.DateTime)
    # 原因分析
    self_reason = db.Column(db.String(128))
    # 结果（0：默认值，1：成功，2：失败）
    cur_result = db.Column(db.Integer, default=0)
    # 修改措施
    measure = db.Column(db.String(128))
    # 开始时间
    start_time = db.Column(db.DateTime)
    # 结束时间
    end_time = db.Column(db.DateTime)
    # 提取的信息
    extract = db.Column(db.Text)
    # 任务ID
    task_id = db.Column(db.Integer)
    cn_android_id = db.Column(db.Integer, db.ForeignKey('cn_android.id'))

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def start_time_aware(self):
        return self.start_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def end_time_aware(self):
        return self.end_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class EnAndroidRecord(db.Model):
    __tablename__ = 'en_android_record'
    id = db.Column(db.Integer, primary_key=True)
    l_class = db.Column(db.Integer)
    mobile_ip = db.Column(db.String(32))
    router_host = db.Column(db.String(32))
    router_port = db.Column(db.Integer)
    extend_device = db.Column(db.String(32))
    created_at = db.Column(db.DateTime)
    # 原因分析
    self_reason = db.Column(db.String(128))
    # 结果（0：默认值，1：成功，2：失败）
    cur_result = db.Column(db.Integer, default=0)
    # 修改措施
    measure = db.Column(db.String(128))
    # 开始时间
    start_time = db.Column(db.DateTime)
    # 结束时间
    end_time = db.Column(db.DateTime)
    # 提取的信息
    extract = db.Column(db.Text)
    # 任务ID
    task_id = db.Column(db.Integer)
    en_android_id = db.Column(db.Integer, db.ForeignKey('en_android.id'))

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def start_time_aware(self):
        return self.start_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def end_time_aware(self):
        return self.end_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class CnIOSRecord(db.Model):
    __tablename__ = 'cn_ios_record'
    id = db.Column(db.Integer, primary_key=True)
    l_class = db.Column(db.Integer)
    mobile_ip = db.Column(db.String(32))
    router_host = db.Column(db.String(32))
    router_port = db.Column(db.Integer)
    extend_device = db.Column(db.String(32))
    created_at = db.Column(db.DateTime)
    # 原因分析
    self_reason = db.Column(db.String(128))
    # 结果（0：默认值，1：成功，2：失败）
    cur_result = db.Column(db.Integer, default=0)
    # 修改措施
    measure = db.Column(db.String(128))
    # 开始时间
    start_time = db.Column(db.DateTime)
    # 结束时间
    end_time = db.Column(db.DateTime)
    # 提取的信息
    extract = db.Column(db.Text)
    # 任务ID
    task_id = db.Column(db.Integer)
    cn_ios_id = db.Column(db.Integer, db.ForeignKey('cn_ios.id'))

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def start_time_aware(self):
        return self.start_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def end_time_aware(self):
        return self.end_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class EnIOSRecord(db.Model):
    __tablename__ = 'en_ios_record'
    id = db.Column(db.Integer, primary_key=True)
    l_class = db.Column(db.Integer)
    mobile_ip = db.Column(db.String(32))
    router_host = db.Column(db.String(32))
    router_port = db.Column(db.Integer)
    extend_device = db.Column(db.String(32))
    created_at = db.Column(db.DateTime)
    # 原因分析
    self_reason = db.Column(db.String(128))
    # 结果（0：默认值，1：成功，2：失败）
    cur_result = db.Column(db.Integer, default=0)
    # 修改措施
    measure = db.Column(db.String(128))
    # 开始时间
    start_time = db.Column(db.DateTime)
    # 结束时间
    end_time = db.Column(db.DateTime)
    # 提取的信息
    extract = db.Column(db.Text)
    # 任务ID
    task_id = db.Column(db.Integer)
    en_ios_id = db.Column(db.Integer, db.ForeignKey('en_ios.id'))

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def start_time_aware(self):
        return self.start_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def end_time_aware(self):
        return self.end_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class Task(db.Model):
    __tablename__ = 'task'
    id = db.Column(db.Integer, primary_key=True)
    # 任务名
    name = db.Column(db.String(64))
    created_at = db.Column(db.DateTime, default=db.func.now())
    cn_android_total = db.Column(db.Integer, default=0)
    en_android_total = db.Column(db.Integer, default=0)
    cn_ios_total = db.Column(db.Integer, default=0)
    en_ios_total = db.Column(db.Integer, default=0)
    # 巡检成功的数量
    success = db.Column(db.Integer, default=0)
    # 巡检失败的数量
    failed = db.Column(db.Integer, default=0)
    # 状态(0: 未开始, 1: 结束, 2: 暂停, 3: 进行中)
    status = db.Column(db.Integer, default=0)
    # 任务进度
    progress = db.Column(db.Float, default=0)
    # 未修复数
    unrepaired = db.Column(db.Integer, default=0)
    # 任务模式(block: 阻断, identify: 识别)
    mode = db.Column(db.String(32))
    task_record = db.relationship("TaskRecord", backref='task', lazy='dynamic')

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class TaskRecord(db.Model):
    __tablename__ = 'task_record'
    id = db.Column(db.Integer, primary_key=True)
    # 应用ID
    appid = db.Column(db.Integer)
    # 平台(1: 国内android, 2: 国外android, 3: 国内IOS, 4: 国外IOS)
    platform = db.Column(db.Integer)
    # 结果（0：默认值，1：成功，2：失败）
    result = db.Column(db.Integer, default=0)
    # 是否已完成修改(0: 否, 1: 是)
    repaired = db.Column(db.Integer, default=0)
    task_id = db.Column(db.Integer, db.ForeignKey('task.id'))


class SnfVersion(db.Model):
    __tablename__ = 'snf_version'
    id = db.Column(db.Integer, primary_key=True)
    ver = db.Column(db.String(32))
    subver = db.Column(db.String(32))
    vertime = db.Column(db.String(64))
    items = db.Column(db.String(32))
    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)
    cfgitem = db.relationship("CfgItem", backref='snfversion', lazy='dynamic', cascade="all,delete,delete-orphan")


class CfgItem(db.Model):
    __tablename__ ='cfg_item'
    id = db.Column(db.Integer, primary_key=True)
    ctitle = db.Column(db.String(64))
    cdata = db.Column(db.String(64))
    ctype = db.Column(db.String(16))
    cactive = db.Column(db.String(16))
    cdesc = db.Column(db.String(512))
    flag = db.Column(db.String(64))
    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)
    snfversion_id = db.Column(db.Integer, db.ForeignKey('snf_version.id'))


class Sniffer(db.Model):
    __tablename__ ='sniffer'
    id = db.Column(db.Integer, primary_key=True)
    classname = db.Column(db.String(64))
    _class = db.Column(db.String(32))
    servertype = db.Column(db.String(32))
    name = db.Column(db.String(128))
    value = db.Column(db.String(32), nullable=False)
    enname = db.Column(db.String(128))
    enclassname = db.Column(db.String(128))
    ntop = db.Column(db.Integer)

    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)

    __table_args__ = (
        UniqueConstraint('domestic', 'value', name='_domestic_value_uc'),
    )

    qoe_cucc_class_id = db.Column(db.Integer, db.ForeignKey('qoe_cucc_class.id'))
    qoe_cmcc_class_id = db.Column(db.Integer, db.ForeignKey('qoe_cmcc_class.id'))
    qoe_ctcc_class_id = db.Column(db.Integer, db.ForeignKey('qoe_ctcc_class.id'))

    cn_android_id = db.Column(db.Integer, db.ForeignKey('cn_android.id'))
    cn_ios_id = db.Column(db.Integer, db.ForeignKey('cn_ios.id'))
    en_android_id = db.Column(db.Integer, db.ForeignKey('en_android.id'))
    en_ios_id = db.Column(db.Integer, db.ForeignKey('en_ios.id'))

    knport = db.relationship("Knport", backref='sniffer', lazy='dynamic', cascade="all,delete,delete-orphan")
    ipport = db.relationship("Ipport", backref='sniffer', lazy='dynamic', cascade="all,delete,delete-orphan")
    varkey = db.relationship("Varkey", backref='sniffer', lazy='dynamic', cascade="all,delete,delete-orphan")
    string = db.relationship("String", backref='sniffer', lazy='dynamic', cascade="all,delete,delete-orphan")
    ctstring = db.relationship("Ctstring", backref='sniffer', lazy='dynamic', cascade="all,delete,delete-orphan")
    uastring = db.relationship("Uastring", backref='sniffer', lazy='dynamic', cascade="all,delete,delete-orphan")
    rfstring = db.relationship("Rfstring", backref='sniffer', lazy='dynamic', cascade="all,delete,delete-orphan")
    payloadstring = db.relationship("PayloadString", backref='sniffer', lazy='dynamic', cascade="all,delete,delete-orphan")
    platform_supports = db.relationship('SnifferPlatformSupport', backref='sniffer', lazy='dynamic', cascade="all,delete,delete-orphan")


class Knport(db.Model):
    __tablename__ ='knport'
    id = db.Column(db.Integer, primary_key=True)
    port = db.Column(db.String(32), nullable=False, index=True)
    proto = db.Column(db.String(32), nullable=False)
    isreal = db.Column(db.String(32), nullable=False)
    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)

    __table_args__ = (
        UniqueConstraint('port', 'proto', 'domestic', name='_port_proto_domestic_uc'),
    )

    sniffer_id = db.Column(db.Integer, db.ForeignKey('sniffer.id'))


class Ipport(db.Model):
    __tablename__ ='ipport'
    id = db.Column(db.Integer, primary_key=True)
    ip = db.Column(db.String(32), nullable=False, index=True)
    port = db.Column(db.String(32))
    proto = db.Column(db.String(32), nullable=False)
    iptype = db.Column(db.String(32), nullable=False)
    isreal = db.Column(db.String(32), nullable=False)
    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)

    __table_args__ = (
        UniqueConstraint('ip', 'port', 'proto', 'domestic', name='_ip_port_proto_domestic_uc'),
    )

    sniffer_id = db.Column(db.Integer, db.ForeignKey('sniffer.id'))


class Varkey(db.Model):
    __tablename__ ='varkey'
    id = db.Column(db.Integer, primary_key=True)
    subsection = db.Column(db.String(64))
    sub_name = db.Column(db.String(128), index=True)
    proto = db.Column(db.String(32), nullable=False)
    value = db.Column(db.String, nullable=False, index=True)
    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)
    video_type = db.Column(db.String(32))

    __table_args__ = (
        UniqueConstraint('proto', 'value', 'domestic', name='_varkey_proto_value_domestic_uc'),
    )

    sniffer_id = db.Column(db.Integer, db.ForeignKey('sniffer.id'))


class String(db.Model):
    __tablename__ ='string'
    id = db.Column(db.Integer, primary_key=True)
    subsection = db.Column(db.String(64))
    sub_name = db.Column(db.String(128), index=True)
    source = db.Column(db.String, nullable=False, index=True)
    value = db.Column(db.String, nullable=False, index=True)
    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)
    video_type = db.Column(db.String(32))
    __table_args__ = (
        UniqueConstraint('source', 'value', 'domestic', name='_source_value_domestic_uc'),
    )

    sniffer_id = db.Column(db.Integer, db.ForeignKey('sniffer.id'))


class Ctstring(db.Model):
    __tablename__ ='ctstring'
    id = db.Column(db.Integer, primary_key=True)
    subsection = db.Column(db.String(64))
    sub_name = db.Column(db.String(128), index=True)
    value = db.Column(db.String, nullable=False, index=True)
    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)
    video_type = db.Column(db.String(32))
    __table_args__ = (
        UniqueConstraint('value', 'domestic', name='_ct_string_value_domestic_uc'),
    )

    sniffer_id = db.Column(db.Integer, db.ForeignKey('sniffer.id'))

class Uastring(db.Model):
    __tablename__ ='uastring'
    id = db.Column(db.Integer, primary_key=True)
    subsection = db.Column(db.String(64))
    sub_name = db.Column(db.String(128), index=True)
    value = db.Column(db.String, nullable=False, index=True)
    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)
    video_type = db.Column(db.String(32))
    __table_args__ = (
        UniqueConstraint('value', 'domestic', name='_ua_string_value_domestic_uc'),
    )

    sniffer_id = db.Column(db.Integer, db.ForeignKey('sniffer.id'))


class Rfstring(db.Model):
    __tablename__ ='rfstring'
    id = db.Column(db.Integer, primary_key=True)
    subsection = db.Column(db.String(64))
    sub_name = db.Column(db.String(128), index=True)
    value = db.Column(db.String, nullable=False, index=True)
    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)
    video_type = db.Column(db.String(32))
    __table_args__ = (
        UniqueConstraint('value', 'domestic', name='_rf_string_value_domestic_uc'),
    )

    sniffer_id = db.Column(db.Integer, db.ForeignKey('sniffer.id'))

class PayloadString(db.Model):
    __tablename__ = 'payload_string'
    id = db.Column(db.Integer, primary_key=True)
    subsection = db.Column(db.String(64))
    sub_name = db.Column(db.String(128), index=True)
    proto = db.Column(db.String(32), nullable=False)
    isbegin = db.Column(db.String(32), nullable=False)
    value = db.Column(db.String, nullable=False, index=True)
    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)
    video_type = db.Column(db.String(32))

    __table_args__ = (
        UniqueConstraint('proto', 'value', 'domestic', name='_proto_value_domestic_uc'),
    )

    sniffer_id = db.Column(db.Integer, db.ForeignKey('sniffer.id'))


class CuccClass(db.Model):
    # 联通
    __tablename__ = 'cucc_class'
    id = db.Column(db.Integer, primary_key=True)
    cucc_type = db.Column(db.Integer)
    cucc_type_name = db.Column(db.String(64))
    cucc_content = db.Column(db.String)

    bigclass = db.relationship("BigClass", backref='cuccclass', lazy='dynamic')



class CtccClass(db.Model):
    # 电信
    __tablename__ = 'ctcc_class'
    id = db.Column(db.Integer, primary_key=True)
    ctcc_type = db.Column(db.Integer)
    ctcc_type_name = db.Column(db.String(64))
    ctcc_content = db.Column(db.String)

    bigclass = db.relationship("BigClass", backref='ctccclass', lazy='dynamic')


class CmccClass(db.Model):
    # 移动
    __tablename__ = 'cmcc_class'
    id = db.Column(db.Integer, primary_key=True)
    cmcc_type = db.Column(db.Integer)
    cmcc_type_name = db.Column(db.String(64))
    cmcc_content = db.Column(db.String)

    bigclass = db.relationship("BigClass", backref='cmccclass', lazy='dynamic')


class QoeCuccClass(db.Model):
    # 联通
    __tablename__ = 'qoe_cucc_class'
    id = db.Column(db.Integer, primary_key=True)
    cucc_qoe_value = db.Column(db.Integer)
    cucc_qoe_type = db.Column(db.String)
    cucc_qoe_name = db.Column(db.String)

    bigclass = db.relationship("BigClass", backref='qoecuccclass', lazy='dynamic')
    sniffer = db.relationship("Sniffer", backref='qoecuccclass', lazy='dynamic')


class QoeCtccClass(db.Model):
    # 电信
    __tablename__ = 'qoe_ctcc_class'
    id = db.Column(db.Integer, primary_key=True)
    ctcc_qoe_value = db.Column(db.Integer)
    ctcc_qoe_type = db.Column(db.String)
    ctcc_qoe_name = db.Column(db.String)

    bigclass = db.relationship("BigClass", backref='qoectccclass', lazy='dynamic')
    sniffer = db.relationship("Sniffer", backref='qoectccclass', lazy='dynamic')


class QoeCmccClass(db.Model):
    # 移动
    __tablename__ = 'qoe_cmcc_class'
    id = db.Column(db.Integer, primary_key=True)
    cmcc_qoe_value = db.Column(db.Integer)
    cmcc_qoe_type = db.Column(db.String)
    cmcc_qoe_name = db.Column(db.String)

    bigclass = db.relationship("BigClass", backref='qoecmccclass', lazy='dynamic')
    sniffer = db.relationship("Sniffer", backref='qoecmccclass', lazy='dynamic')


class BigClass(db.Model):
    __tablename__ = 'big_class'
    id = db.Column(db.Integer, primary_key=True)
    class_id = db.Column(db.Integer, nullable=False)
    classname = db.Column(db.String(64), nullable=False)
    enclassname = db.Column(db.String(64), nullable=False)

    # 国内还是国外（0：国内，1：国外）
    domestic = db.Column(db.Integer, default=0)

    __table_args__ = (
        UniqueConstraint('class_id', 'domestic', name='_class_id_domestic_uc'),
    )
    cucc_class_id = db.Column(db.Integer, db.ForeignKey('cucc_class.id'))
    ctcc_class_id = db.Column(db.Integer, db.ForeignKey('ctcc_class.id'))
    cmcc_class_id = db.Column(db.Integer, db.ForeignKey('cmcc_class.id'))

    qoe_cucc_class_id = db.Column(db.Integer, db.ForeignKey('qoe_cucc_class.id'))
    qoe_ctcc_class_id = db.Column(db.Integer, db.ForeignKey('qoe_ctcc_class.id'))
    qoe_cmcc_class_id = db.Column(db.Integer, db.ForeignKey('qoe_cmcc_class.id'))


class HistoryLog(db.Model):
    __tablename__ = 'history_log'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    domestic = db.Column(db.Integer)
    operation_type = db.Column(db.String(50))
    operation_data = db.Column(db.JSON)
    description = db.Column(db.String)

    created_at = db.Column(db.DateTime, default=db.func.now())
    status = db.Column(db.Integer, default=1)
    error_msg = db.Column(db.String(256))
    user = db.relationship("User", backref=db.backref('history_log', lazy='dynamic'))

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

class Versioning(db.Model):
    __tablename__ = 'versioning'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    district = db.Column(db.String(64), nullable=False)
    version = db.Column(db.String(64), nullable=False)
    discorb = db.Column(db.String(64), nullable=False)
    version_time = db.Column(db.DateTime, default=db.func.now())
    notes = db.Column(db.String(256), nullable=False)
    publish_url = db.Column(db.String(256))
    db_url = db.Column(db.String(256))

    @property
    def version_time_aware(self):
        return self.version_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

class HuaweiApp(db.Model):
    __tablename__ = 'huawei_app'
    id = db.Column(db.Integer, primary_key=True)
    appname = db.Column(db.String, nullable=False)
    app_id = db.Column(db.String, nullable=False, unique=True)
    pkg_name = db.Column(db.String, nullable=False)
    version = db.Column(db.String, nullable=False)
    developer = db.Column(db.String)
    update_time = db.Column(db.DateTime, default=db.func.now())
    tags = db.Column(db.String)
    md5 = db.Column(db.String)
    download_url = db.Column(db.String)
    memo = db.Column(db.String)
    kindName = db.Column(db.String)
    downCountDesc = db.Column(db.Integer)
    sizeDesc = db.Column(db.String)
    score = db.Column(db.String)
    isgame = db.Column(db.Integer)
    new = db.Column(db.Integer)
    value = db.Column(db.String(32))
    top = db.Column(db.Integer)
    rating = db.Column(db.String)

    delete = db.Column(db.Integer, default=0)

    __table_args__ = (
        db.UniqueConstraint('pkg_name', 'appname', name='uk_pkg_name_appname'),
    )

    @property
    def update_time_aware(self):
        return self.update_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class SpiderHistory(db.Model):
    __tablename__ = 'spider_history'

    id = db.Column(db.Integer, primary_key=True)
    store = db.Column(db.String(50), nullable=False)  
    script_type = db.Column(db.String(50), nullable=False)  
    status = db.Column(db.Integer, default=1)
    start_time = db.Column(db.DateTime, nullable=False)  
    end_time = db.Column(db.DateTime, nullable=False)  
    total_count = db.Column(db.Integer, default=0)  
    new_count = db.Column(db.Integer, default=0)  
    update_count = db.Column(db.Integer, default=0)  
    fail_count = db.Column(db.Integer, default=0) 
    error_log = db.Column(db.Text)  

    def __repr__(self):
        return f'<SpiderHistory {self.store} {self.script_type} {self.start_time}>'


class GooglePlayApp(db.Model):
    __tablename__ = 'google_play_app'
    id = db.Column(db.Integer, primary_key=True)
    app_id = db.Column(db.String, nullable=False, unique=True)
    pkg_name = db.Column(db.String, nullable=False)
    appname = db.Column(db.String, nullable=False)
    version = db.Column(db.String, nullable=False)
    downCountDesc = db.Column(db.Integer)
    minInstalls = db.Column(db.Integer)
    score = db.Column(db.String)
    scoreText = db.Column(db.String)
    developerWebsite = db.Column(db.String)
    genreId = db.Column(db.String)
    developer = db.Column(db.String)
    update_time = db.Column(db.DateTime, default=db.func.now())
    tags = db.Column(db.String)
    download_url = db.Column(db.String)
    ratings = db.Column(db.Integer)
    reviews = db.Column(db.Integer)
    description = db.Column(db.Text)
    isgame = db.Column(db.Integer, default=0)
    new = db.Column(db.Integer, default=0)
    value = db.Column(db.String(32))
    top = db.Column(db.Integer)
    rating = db.Column(db.String)

    delete = db.Column(db.Integer, default=0)

    __table_args__ = (
        db.UniqueConstraint('pkg_name', 'appname', name='uk_pkg_name_appname'),
    )

    @property
    def update_time_aware(self):
        return self.update_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class AppStoreApp(db.Model):
    __tablename__ = 'app_store_app'
    id = db.Column(db.Integer, primary_key=True)
    app_id = db.Column(db.String, nullable=False, unique=True)
    pkg_name = db.Column(db.String, nullable=False)
    appname = db.Column(db.String, nullable=False)
    version = db.Column(db.String, nullable=False)
    developer = db.Column(db.String)
    update_time = db.Column(db.DateTime, default=db.func.now())
    tags = db.Column(db.String)
    download_url = db.Column(db.String)
    sizeDesc = db.Column(db.String)
    score = db.Column(db.String)
    primaryGenreId = db.Column(db.String)
    icon = db.Column(db.String)
    reviews = db.Column(db.Integer)
    description = db.Column(db.Text)
    domestic = db.Column(db.Integer, nullable=False, default=0)
    isgame = db.Column(db.Integer, default=0)
    new = db.Column(db.Integer, default=0)
    value = db.Column(db.String(32))
    top = db.Column(db.Integer)
    rating = db.Column(db.String)

    delete = db.Column(db.Integer, default=0)

    __table_args__ = (
        db.UniqueConstraint('pkg_name', 'appname', name='uk_pkg_name_appname'),
    )

    @property
    def update_time_aware(self):
        return self.update_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class XiaomiApp(db.Model):
    __tablename__ = 'xiaomi_app'
    id = db.Column(db.Integer, primary_key=True)
    appname = db.Column(db.String, nullable=False)
    app_id = db.Column(db.String, nullable=False, unique=True)
    pkg_name = db.Column(db.String, nullable=False)
    version = db.Column(db.String, nullable=False)
    developer = db.Column(db.String)
    update_time = db.Column(db.DateTime, default=db.func.now())
    tags = db.Column(db.String)
    download_url = db.Column(db.String)
    downCountDesc = db.Column(db.Integer)
    sizeDesc = db.Column(db.String)
    score = db.Column(db.String)
    kindName = db.Column(db.String)
    isgame = db.Column(db.Integer, default=0)
    new = db.Column(db.Integer, default=0)
    value = db.Column(db.String(32))
    top = db.Column(db.Integer)
    rating = db.Column(db.String)

    delete = db.Column(db.Integer, default=0)

    __table_args__ = (
        db.UniqueConstraint('pkg_name', 'appname', name='uk_pkg_name_appname'),
    )

    @property
    def update_time_aware(self):
        return self.update_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class TencentApp(db.Model):
    __tablename__ = 'tencent_app'
    id = db.Column(db.Integer, primary_key=True)
    appname = db.Column(db.String, nullable=False)
    app_id = db.Column(db.String, nullable=False, unique=True)
    pkg_name = db.Column(db.String, nullable=False)
    version = db.Column(db.String, nullable=False)
    developer = db.Column(db.String)
    update_time = db.Column(db.DateTime, default=db.func.now())
    tags = db.Column(db.String)
    md5 = db.Column(db.String)
    download_url = db.Column(db.String)
    downCountDesc = db.Column(db.Integer)
    score = db.Column(db.String)
    isgame = db.Column(db.Integer, default=0)
    new = db.Column(db.Integer, default=0)
    value = db.Column(db.String(32))
    top = db.Column(db.Integer)
    rating = db.Column(db.String)

    delete = db.Column(db.Integer, default=0)

    __table_args__ = (
        db.UniqueConstraint('pkg_name', 'appname', name='uk_pkg_name_appname'),
    )

    @property
    def update_time_aware(self):
        return self.update_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class MicrosoftApp(db.Model):
    __tablename__ = 'microsoft_app'
    id = db.Column(db.Integer, primary_key=True)
    app_id = db.Column(db.String, nullable=False, unique=True)
    appname = db.Column(db.String, nullable=False)
    developer = db.Column(db.String)
    update_time = db.Column(db.DateTime, default=db.func.now())
    tags = db.Column(db.String)
    download_url = db.Column(db.String)
    reviews = db.Column(db.Integer)
    score = db.Column(db.String)
    description = db.Column(db.Text)
    isgame = db.Column(db.Integer, default=0)
    new = db.Column(db.Integer, default=0)
    value = db.Column(db.String(32))
    top = db.Column(db.Integer)
    rating = db.Column(db.String)

    delete = db.Column(db.Integer, default=0)

    __table_args__ = (
        db.UniqueConstraint('app_id', name='uk_microsoft_store_app_id'),
    )

    @property
    def update_time_aware(self):
        return self.update_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class AndroidTop(db.Model):
    __tablename__ = 'android_top'
    id = db.Column(db.Integer, primary_key=True)
    pkg_name = db.Column(db.String, nullable=False)
    value = db.Column(db.String(32))
    appname = db.Column(db.String)
    top = db.Column(db.Integer)
    rating = db.Column(db.String)
    domestic = db.Column(db.Integer, nullable=False, default=0)

    yyb_top = db.Column(db.Integer)
    huawei_top = db.Column(db.Integer)
    xiaomi_top = db.Column(db.Integer)
    google_play_top = db.Column(db.Integer)

    yyb_id = db.Column(db.Integer, db.ForeignKey('tencent_app.id'))
    huawei_id = db.Column(db.Integer, db.ForeignKey('huawei_app.id'))
    xiaomi_id = db.Column(db.Integer, db.ForeignKey('xiaomi_app.id'))
    google_play_id = db.Column(db.Integer, db.ForeignKey('google_play_app.id'))


    yyb = db.relationship('TencentApp', foreign_keys=[yyb_id])
    huawei = db.relationship('HuaweiApp', foreign_keys=[huawei_id])
    xiaomi = db.relationship('XiaomiApp', foreign_keys=[xiaomi_id])
    google_play = db.relationship('GooglePlayApp', foreign_keys=[google_play_id])

class ApplicationBase(db.Model):
    __tablename__ = 'application_base'
    
    id = db.Column(db.Integer, primary_key=True)
    top = db.Column(db.Integer)
    value = db.Column(db.String(32))
    pkg_name = db.Column(db.String)
    appname = db.Column(db.String, nullable=False)
    rating = db.Column(db.String)
    ios_top = db.Column(db.Integer)
    android_top = db.Column(db.Integer)
    pc_top = db.Column(db.Integer)
    web_top = db.Column(db.Integer)
    domestic = db.Column(db.Integer, nullable=False, default=0)

    def __repr__(self):
        return f'<ApplicationBase {self.appname}>'


class ApplicationBaseSnapshot(db.Model):
    __tablename__ = 'application_base_snapshot'
    id = db.Column(db.Integer, primary_key=True)
    app_key = db.Column(db.String(255), index=True)
    value = db.Column(db.String(32))
    appname = db.Column(db.String)
    top = db.Column(db.Integer)
    domestic = db.Column(db.Integer)
    snapshot_type = db.Column(db.String(64))

    def __repr__(self):
        return f'<ApplicationBase {self.appname}>'

class BaokuApp(db.Model):
    __tablename__ = 'baoku_app'
    id = db.Column(db.Integer, primary_key=True)
    appname = db.Column(db.String, nullable=False)
    app_id = db.Column(db.String, nullable=False, unique=True)
    tags = db.Column(db.String)
    update_time = db.Column(db.DateTime, default=db.func.now())
    download_url = db.Column(db.String)
    sizeDesc = db.Column(db.String)
    score = db.Column(db.String)
    description = db.Column(db.Text)
    isgame = db.Column(db.Integer, default=0)
    new = db.Column(db.Integer, default=0)
    value = db.Column(db.String(32))
    top = db.Column(db.Integer)
    rating = db.Column(db.String)

    delete = db.Column(db.Integer, default=0)

    __table_args__ = (
        UniqueConstraint('app_id', name='_app_id_uc'),
    )

    @property
    def update_time_aware(self):
        return self.update_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

class ChinazWeb(db.Model):
    __tablename__ = 'chinaz_web'

    id = db.Column(db.Integer, primary_key=True)
    value = db.Column(db.String(32))
    appname = db.Column(db.String)
    top = db.Column(db.Integer)
    domain = db.Column(db.String(255))
    download_url = db.Column(db.String)
    domestic = db.Column(db.Integer, nullable=False, default=0)
    new = db.Column(db.Integer, default=0)

    delete = db.Column(db.Integer, default=0)

    def __repr__(self):
        return f'<ChinazWeb {self.name}>'

class SemrushWeb(db.Model):
    __tablename__ = 'semrush_web'
    id = db.Column(db.Integer, primary_key=True)
    appname = db.Column(db.String, nullable=False)
    domain = db.Column(db.String, nullable=False)
    traffic = db.Column(db.Integer, default=0)
    domestic = db.Column(db.Integer, nullable=False, default=0)
    value = db.Column(db.String(32))
    top = db.Column(db.Integer)
    download_url = db.Column(db.String)
    new = db.Column(db.Integer, default=0)

    delete = db.Column(db.Integer, default=0)

    def __repr__(self):
        return f'<SemrushWeb {self.appname}>'

class WebTop(db.Model):
    __tablename__ = 'web_top'
    id = db.Column(db.Integer, primary_key=True)
    value = db.Column(db.String(32))
    appname = db.Column(db.String)
    domain = db.Column(db.String, nullable=False)
    top = db.Column(db.Integer)
    rating = db.Column(db.String)
    domestic = db.Column(db.Integer, nullable=False, default=0)

    chinaz_top = db.Column(db.Integer)
    semrush_top = db.Column(db.Integer)

    chinaz_id = db.Column(db.Integer, db.ForeignKey('chinaz_web.id'))
    semrush_id = db.Column(db.Integer, db.ForeignKey('semrush_web.id'))

    chinaz = db.relationship('ChinazWeb', foreign_keys=[chinaz_id])
    semrush = db.relationship('SemrushWeb', foreign_keys=[semrush_id])


class PcTop(db.Model):
    __tablename__ = 'pc_top'
    id = db.Column(db.Integer, primary_key=True)
    value = db.Column(db.String(32))
    appname = db.Column(db.String)
    top = db.Column(db.Integer)
    rating = db.Column(db.String)
    domestic = db.Column(db.Integer, nullable=False, default=0)

    microsoft_top = db.Column(db.Integer)
    baoku_top = db.Column(db.Integer)

    microsoft_id = db.Column(db.Integer, db.ForeignKey('microsoft_app.id'))
    baoku_id = db.Column(db.Integer, db.ForeignKey('baoku_app.id'))


    microsoft = db.relationship('MicrosoftApp', foreign_keys=[microsoft_id])
    baoku = db.relationship('BaokuApp', foreign_keys=[baoku_id])


class SnifferPlatformSupport(db.Model):
    __tablename__ = 'sniffer_platform_support'

    id = db.Column(db.Integer, primary_key=True)
    value = db.Column(db.String(32), nullable=False)
    sniffer_id = db.Column(db.Integer, db.ForeignKey('sniffer.id'), nullable=False)

    android = db.Column(db.Integer, default=0, nullable=False)
    ios = db.Column(db.Integer, default=0, nullable=False)
    pc = db.Column(db.Integer, default=0, nullable=False)
    web = db.Column(db.Integer, default=0, nullable=False)

    domestic = db.Column(db.Integer, nullable=False)



class OperationTypeMeta(type):
    def __getattr__(cls, name):
        cls._load_once()
        if name in cls._permissions:
            result = cls._permissions[name]
            return result
        raise AttributeError(f"OperationType没有{name}这个操作")

class OperationType(metaclass=OperationTypeMeta):
    _loaded = False
    _permissions = {}
    
    @classmethod
    def _load_once(cls):
        if not cls._loaded:
            permissions = Permission.query.all()
            for perm in permissions:
                setattr(cls, perm.name.upper(), perm.name.lower())
                cls._permissions[perm.name.upper()] = perm.name.lower()
            cls._loaded = True

HistoryLog.OperationType = OperationType


class StreamRecognition(db.Model):
    __tablename__ = 'stream_recognition'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String, nullable=False)
    value = db.Column(db.Integer, nullable=False)
    platform = db.Column(db.String, nullable=False)
    package = db.Column(db.String)
    result = db.Column(db.Integer, default=0)
    pcap_name = db.Column(db.String)
    created_at = db.Column(db.DateTime, default=db.func.now())
    updated_at = db.Column(db.DateTime, default=db.func.now(), onupdate=db.func.now())

    @property
    def created_at_aware(self):
        return self.created_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)

    @property
    def updated_at_aware(self):
        return self.updated_at.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)


class XdrDetail(db.Model):
    __tablename__ = 'xdr_detail'
    id = db.Column(db.Integer, primary_key=True)
    value = db.Column(db.String, nullable=False)
    xdr_type = db.Column(db.String)
    big_class = db.Column(db.String)
    name = db.Column(db.String)
    upstream_traffic = db.Column(db.String)
    downstream_traffic = db.Column(db.String)
    upstream_packets = db.Column(db.String)
    downstream_packets = db.Column(db.String)
    pcap_name = db.Column(db.String)
    proportion = db.Column(db.String)
    total = db.Column(db.String)
    stream_recognition_id = db.Column(db.Integer, db.ForeignKey('stream_recognition.id'))

