import { ta } from 'element-plus/es/locale';
import request from '../utils/request';

export const loginApi = (username: string, password: string) => {
    return request({
        url: '/token',
        method: 'post',
        data: {
            username: username,
            password: password
        }
    });
};

export const cnAndroidData = (tableInfo: any) => {
    let startTime = "";
    let endTime = ""
    if (tableInfo.time != null && tableInfo.time.length > 0) {
        startTime = tableInfo.time[0].toLocaleDateString("zh-CN");
        endTime = tableInfo.time[1].toLocaleDateString("zh-CN");
    }
    return request({
        url: '/cn_android',
        method: 'post',
        data: {
           delete: tableInfo.delete,
           name: tableInfo.name,
           l_class: tableInfo.l_class,
           package: tableInfo.package,
           priority: tableInfo.priority,
           result: tableInfo.result,
           author: tableInfo.author,
           taskId: tableInfo.taskId,
           script_category: tableInfo.script_category,
           pageIndex: tableInfo.pageIndex,
           pageSize: tableInfo.pageSize,
           startTime: startTime,
           endTime: endTime
        }
    });
};

export const enAndroidData = (tableInfo: any) => {
    let startTime = "";
    let endTime = ""
    if (tableInfo.time != null && tableInfo.time.length > 0) {
        startTime = tableInfo.time[0].toLocaleDateString("zh-CN");
        endTime = tableInfo.time[1].toLocaleDateString("zh-CN");
    }
    return request({
        url: '/en_android',
        method: 'post',
        data: {
           delete: tableInfo.delete,
           l_class: tableInfo.l_class,
           name: tableInfo.name,
           package: tableInfo.package,
           priority: tableInfo.priority,
           result: tableInfo.result,
           author: tableInfo.author,
           taskId: tableInfo.taskId,
           pageIndex: tableInfo.pageIndex,
           pageSize: tableInfo.pageSize,
           startTime: startTime,
           endTime: endTime
        }
    });
};

export const cnIOSData = (tableInfo: any) => {
    let startTime = "";
    let endTime = ""
    if (tableInfo.time != null && tableInfo.time.length > 0) {
        startTime = tableInfo.time[0].toLocaleDateString("zh-CN");
        endTime = tableInfo.time[1].toLocaleDateString("zh-CN");
    }
    return request({
        url: '/cn_ios',
        method: 'post',
        data: {
           delete: tableInfo.delete,
           l_class: tableInfo.l_class,
           name: tableInfo.name,
           package: tableInfo.package,
           priority: tableInfo.priority,
           result: tableInfo.result,
           author: tableInfo.author,
           taskId: tableInfo.taskId,
           pageIndex: tableInfo.pageIndex,
           pageSize: tableInfo.pageSize,
           startTime: startTime,
           endTime: endTime
        }
    });
};

export const enIOSData = (tableInfo: any) => {
    let startTime = "";
    let endTime = ""
    if (tableInfo.time != null && tableInfo.time.length > 0) {
        startTime = tableInfo.time[0].toLocaleDateString("zh-CN");
        endTime = tableInfo.time[1].toLocaleDateString("zh-CN");
    }
    return request({
        url: '/en_ios',
        method: 'post',
        data: {
           delete: tableInfo.delete,
           l_class: tableInfo.l_class,
           name: tableInfo.name,
           package: tableInfo.package,
           priority: tableInfo.priority,
           result: tableInfo.result,
           author: tableInfo.author,
           taskId: tableInfo.taskId,
           pageIndex: tableInfo.pageIndex,
           pageSize: tableInfo.pageSize,
           startTime: startTime,
           endTime: endTime
        }
    });
};

// export function dataInput(data: object) {
//     return request({
//         url: "/script_upload",
//         method: "post",
//         headers: { "Content-Type": "multipart/form-data" },
//         data: data
//     })
// };

export function dataInput(data: any) {
    return request({
        url: "/script_upload",
        method: "post",
        headers: { "Content-Type": "multipart/form-data" },
        data: {
            file: data.file,
            author: data.author,
            script_category: data.script_category
        }
    })
};

export function getAuthors() {
    return request({
        url: "/authors",
        method: "get"
    })
}

export function getScriptNum() {
    return request({
        url: "/script_num",
        method: "get"
    })
}

export function getScriptResult() {
    return request({
        url: "/script_result",
        method: "get"
    })
}

export function getDateScriptResult(startTime: any, endTime: any) {
    return request({
        url: "/script_result",
        method: "post",
        data: {
            startTime: startTime,
            endTime: endTime
        }
    })
}

export const editScript = (scriptType: string, tb: any, saveOnly: number) => {
    return request({
        url: "/edit_script",
        method: "post",
        data: {
            id: tb.id,
            name: tb.name,
            store: tb.store,
            author: tb.author,
            reason: tb.reason,
            scriptType: scriptType,
            saveOnly: saveOnly,
            record_id: tb.record_id,
            self_reason: tb.self_reason,
            measure: tb.measure,
            designate: tb.designate,
        }
    })
}

export const exeScript = (scriptType: string, tb: any) => {
    return request({
        url: "/exe_script",
        method: "post",
        data: {
            id: tb.id,
            author: tb.author,
            scriptType: scriptType
        }
    })
}

export const delScript = (scriptType: string, id: number, del_store: number) => {
    return request({
        url: "/del_script",
        method: "post",
        data: {
            id: id,
            delStore: del_store,
            scriptType: scriptType
        }
    })
}

export function devicesStatus(count: number, scriptType: string) {
    return request({
        url: "/devices",
        method: "post",
        data: {
            count: count,
            scriptType: scriptType
        }
    })
}

export function priorityStatus() {
    return request({
        url: "/priority",
        method: "get"
    })
}

export function addInspection(tb: any) {
    return request({
        url: "/inspection",
        method: "post",
        headers: { "Content-Type": "multipart/form-data" },
        data: {
            name: tb.name,
            platform: tb.type.join('+'),
            mode: tb.mode,
            file: tb.fileList.length != 0 ? tb.fileList[0].raw : null
        }
    })
}


export function checkInspection() {
    return request({
        url: "/inspection",
        method: "get"
    })
}


export function refreshInspection(scriptType: string) {
    return request({
        url: "/inspection_refresh",
        method: "post",
        data: {
            scriptType: scriptType
        }
    })
}


export function queryInspection(tb: any) {
    return request({
        url: "/inspection_record",
        method: "post",
        data: {
            pageIndex: tb.pageIndex,
            pageSize: tb.pageSize
        }
    })
}


export function taskStatus(task_id: number, status_number: number) {
    return request({
        url: "/inspection_status",
        method: "post",
        data: {
            task_id: task_id,
            status_number: status_number
        }
    })
}

export function taskDelete(task_id: number) {
    return request({
        url: "/inspection_delete",
        method: "post",
        data: {
            task_id: task_id
        }
    })
}


export function refreshTask(taskId: number) {
    return request({
        url: "/inspection_record?task_id=" + taskId.toString() ,
        method: "get"
    })
}


export function fetchUserData(tb: any) {
    return request({
        url: "/user_query",
        method: "post",
        data: {
            pageIndex: tb.pageIndex,
            pageSize: tb.pageSize
        }
    })
}


export const editUser = (tb: any) => {
    return request({
        url: "/user_edit",
        method: "post",
        data: {
            id: tb.id,
            roleId: tb.role_id,
            name: tb.name,
        }
    })
}


export const activityUser = (tb: any) => {
    return request({
        url: "/user_activity",
        method: "post",
        data: {
            id: tb.id,
            activity: tb.activity == "是" ? 0 : 1,
        }
    })
}


export const addUser = (tb: any) => {
    return request({
        url: "/user_add",
        method: "post",
        data: {
            name: tb.name,
            password: tb.password,
        }
    })
}


export const getRecordScript = (scriptType: string, tb: any) => {
    return request({
        url: "/script_record",
        method: "post",
        data: {
            id: tb.id,
            scriptType: scriptType,
            pageIndex: tb.pageIndex,
            pageSize: tb.pageSize
        }
    })
}

export const taskDetail = (tb: any) => {
    return request({
        url: '/inspection_detail',
        method: 'post',
        data: {
           id: tb.taskId,
           platform: tb.platform,
           result: tb.result,
           repaired: tb.repaired,
           pageIndex: tb.pageIndex,
           pageSize: tb.pageSize,
        }
    });
};

export const downloadTaskDetail = (tb: any) => {
    return request({
        url: '/inspection_download',
        method: 'post',
        data: {
           id: tb.taskId,
           platform: tb.platform,
           result: tb.result,
           repaired: tb.repaired
        }
    });
}


export const statDetail = (taskId: number) => {
    return request({
        url: '/inspection_detail?task_id=' + taskId,
        method: 'get'
    });
};


export function getTasks() {
    return request({
        url: "/tasks",
        method: "get"
    })
}

export function taskLink() {
    return request({
        url: "/task_link",
        method: "get"
    })
}

export const apiScriptAllocation = (tb: any) => {
    return request({
        url: '/script_allocation',
        method: 'post',
        data: {
           id: tb.id,
           platform: tb.platform,
           result: tb.result,
           to: tb.to,
           count: tb.count
        }
    });
};

export const apiUserDel = (tb: any) => {
    return request({
        url: '/user_del',
        method: 'post',
        data: {
           id: tb.id,
        }
    });
}

export const apiAppTable = (scriptType: string, tableInfo: any) => {
    let startTime = "";
    let endTime = ""
    if (tableInfo.time != null && tableInfo.time.length > 0) {
        startTime = tableInfo.time[0].toLocaleDateString("zh-CN");
        endTime = tableInfo.time[1].toLocaleDateString("zh-CN");
    }
    return request({
        url: '/app_table',
        method: 'post',
        data: {
           scriptType: scriptType,
           delete: tableInfo.delete,
           l_class: tableInfo.l_class,
           name: tableInfo.name,
           package: tableInfo.package,
           priority: tableInfo.priority,
           result: tableInfo.result,
           author: tableInfo.author,
           startTime: startTime,
           endTime: endTime
        }
    });
}


export const snfVersion = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_version',
        method: 'post',
        data: {
           domestic: tableInfo.domestic,
           pageIndex: tableInfo.pageIndex,
           pageSize: tableInfo.pageSize,
        }
    });
};


export const snfVersionItems = () => {
    
    return request({
        url: '/sniffer_config',
        method: 'get',
    });
};


export const snfVersionAdd = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_version_add',
        method: 'post',
        data: {
           domestic: tableInfo.domestic,
           ver: tableInfo.ver,
           items: tableInfo.items,
           subver: tableInfo.subver,
           vertime: tableInfo.vertime
        }
    });
};


export const snfVersionEdit = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_version_edit',
        method: 'post',
        data: {
           id: tableInfo.id,
           ver: tableInfo.ver,
           items: tableInfo.items,
           subver: tableInfo.subver,
           vertime: tableInfo.vertime
        }
    });
};


export const snfVersionDel = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_version_del',
        method: 'post',
        data: {
           id: tableInfo.id,
        }
    });
};


export const snfConfig = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_config',
        method: 'post',
        data: {
           domestic: tableInfo.domestic,
           pageIndex: tableInfo.pageIndex,
           items: tableInfo.items,
           pageSize: tableInfo.pageSize,
        }
    });
};


export const snfConfigAdd = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_config_add',
        method: 'post',
        data: {
           domestic: tableInfo.domestic,
           items: tableInfo.items,
           ctitle: tableInfo.ctitle,
           cdata: tableInfo.cdata,
           ctype: tableInfo.ctype,
           cactive: tableInfo.cactive,
           cdesc: tableInfo.cdesc,
        }
    });
};


export const snfConfigEdit = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_config_edit',
        method: 'post',
        data: {
           id: tableInfo.id,
           ctitle: tableInfo.ctitle,
           cdata: tableInfo.cdata,
           ctype: tableInfo.ctype,
           cactive: tableInfo.cactive,
           cdesc: tableInfo.cdesc
        }
    });
};


export const snfConfigDel = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_config_del',
        method: 'post',
        data: {
           id: tableInfo.id,
        }
    });
};


export const snfApplication = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_application',
        method: 'post',
        data: {
           domestic: tableInfo.domestic,
           attribute: tableInfo.attribute,
           query_value: tableInfo.query_value,
           pageIndex: tableInfo.pageIndex,
           pageSize: tableInfo.pageSize,
        }
    });
};


export const snfApplicationAdd = (tableInfo: any, status: number) => {
    
    return request({
        url: '/sniffer_application_add',
        method: 'post',
        data: {
           id: tableInfo.id,
           ntop: tableInfo.ntop,
           status: status,
           domestic: tableInfo.domestic,
           name: tableInfo.name,
           value: tableInfo.value,
           classname: tableInfo.classname,
           class: tableInfo.class,
           servertype: tableInfo.servertype,
           enname: tableInfo.enname,
           enclassname: tableInfo.enclassname,
           knports: tableInfo.knports,
           ipports: tableInfo.ipports,
           varkeys: tableInfo.varkeys,
           strings: tableInfo.strings,
           ctstrings: tableInfo.ctstrings,
           uastrings: tableInfo.uastrings,
           rfstrings: tableInfo.rfstrings,
           payloadstrings: tableInfo.payloadstrings,
        }
    });
};

export const snfApplicationCheck = (tableInfo: any, status: number) => {
    
    return request({
        url: '/sniffer_application_check',
        method: 'post',
        data: {
           id: tableInfo.id,
           ntop: tableInfo.ntop,
           status: status,
           domestic: tableInfo.domestic,
           name: tableInfo.name,
           value: tableInfo.value,
           classname: tableInfo.classname,
           class: tableInfo.class,
           servertype: tableInfo.servertype,
           enname: tableInfo.enname,
           enclassname: tableInfo.enclassname,
           knports: tableInfo.knports,
           ipports: tableInfo.ipports,
           varkeys: tableInfo.varkeys,
           strings: tableInfo.strings,
           ctstrings: tableInfo.ctstrings,
           uastrings: tableInfo.uastrings,
           rfstrings: tableInfo.rfstrings,
           payloadstrings: tableInfo.payloadstrings,
        }
    });
};


export const snfApplicationDel = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_application_del',
        method: 'post',
        data: {
           id: tableInfo.id,
        }
    });
};


export function ntopLink(cmdType: string) {
    return request({
        url: "/ntop_link?cmdType=" + cmdType,
        method: "get"
    })
}


export function importNtop(tb: any) {
    return request({
        url: "/ntop_link",
        method: "post",
        headers: { "Content-Type": "multipart/form-data" },
        data: {
            type: tb.type,
            file: tb.fileList.length != 0 ? tb.fileList[0].raw : null
        }
    })
}


export function rjPublic(tb: any, config?: any) {
    return request({
        url: "/rj_public",
        method: "post",
        headers: { "Content-Type": "multipart/form-data" },
        timeout: 600000,
        data: {
            version_type: tb.version_type,
            domestic: tb.domestic,
            project: tb.project,
            version: tb.version,
            feature_db_name: tb.feature_db_name,
            dict_name: tb.dict_name,
            name_len: tb.name_len,
            enname_len: tb.enname_len,
            file: tb.fileList.length != 0 ? tb.fileList[0].raw : null,
            discorb: tb.discorb,
            notes: tb.notes
        },
        ...config
    })
}

export function h3cPublic(tb: any) {
    return request({
        url: "/h3c_public",
        method: "post",
        headers: { "Content-Type": "multipart/form-data" },
        timeout: 600000,
        data: {
            version_type: tb.version_type,
            project: tb.project,
            version: tb.version,
            sdk_ver: tb.sdk_ver,
            op: tb.op,
            op_flag: tb.op_flag,
            qoe_flag: tb.qoe_flag,
            p2p_flag: tb.p2p_flag,
            split_top: tb.split_top,
            c_flag: tb.c_flag,
            domestic: tb.domestic,
            discorb: tb.discorb,
            notes: tb.notes
        },
    })
}

// 查询任务进度
export function queryTaskProgress(taskId: string) {
    return request({
        url: `/task_progress`,
        method: "get",
        params: {
            task_id: taskId
        }
    })
}

// 取消任务
export function cancelTask(taskId: string) {
    return request({
        url: `/cancel_task`,
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: {
            task_id: taskId
        }
    })
}

export const snfBigClass = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_big_class',
        method: 'post',
        data: {
           domestic: tableInfo.domestic,
           value: tableInfo.value,
           pageIndex: tableInfo.pageIndex,
           pageSize: tableInfo.pageSize,
        }
    });
};

export const snfCuccClass = () => {
    return request({
        url: '/isp_cucc_class',
        method: 'get'
    });
}

export const snfQoeCuccClass = () => {
    return request({
        url: '/isp_qoe_cucc_class',
        method: 'get'
    });
}

export const snfCtccClass = () => {
    return request({
        url: '/isp_ctcc_class',
        method: 'get'
    });
}

export const snfCtccQoeClass = () => {
    return request({
        url: '/isp_qoe_ctcc_class',
        method: 'get'
    });
}

export const snfCmccClass = () => {
    return request({
        url: '/isp_cmcc_class',
        method: 'get'
    });
}

export const snfCmccQoeClass = () => {
    return request({
        url: '/isp_qoe_cmcc_class',
        method: 'get'
    });
}

export const convertSimplifiedChinese = (text: string) => {
    
    return request({
        url: '/sniffer_convert_simplified_chinese',
        method: 'post',
        data: {
           name: text
        }
    });
};


export const snfBigClassAll = () => {
    
    return request({
        url: '/sniffer_big_class',
        method: 'get',
    });
};


export const snfBigClassAdd = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_big_class_add',
        method: 'post',
        data: {
           domestic: tableInfo.domestic,
           class: tableInfo.class,
           classname: tableInfo.classname,
           enclassname: tableInfo.enclassname,
        }
    });
};

export const snfBigClassEdit = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_big_class_edit',
        method: 'post',
        data: {
           id: tableInfo.id,
           domestic: tableInfo.domestic,
           class: tableInfo.class,
           classname: tableInfo.classname,
           enclassname: tableInfo.enclassname,
        }
    });
};


export const snfBigClassDel = (tableInfo: any) => {
    
    return request({
        url: '/sniffer_big_class_del',
        method: 'post',
        data: {
           id: tableInfo.id,
        }
    });
};

export const downloadAllFiles = async (url: string[]) => {
    const firstUrl = url[0];
	const prefix = firstUrl.substring(0, firstUrl.lastIndexOf('/')); 

	const ids = url.map(url => {
		return url.split('/').pop(); 
	});
	const combinedUrl = `${prefix}/${ids.join(',')}`;

 	const a = document.createElement('a');
    a.href = combinedUrl;

    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

export const snfApplicationMax = (domestic: number) => {
    return request({
        url: '/sniffer_application_max?domestic=' + domestic,
        method: 'get'
    });
};

export const historyLog = (tableInfo: any) => {
    let startTime = "";
    let endTime = ""

    if (tableInfo.time != null && tableInfo.time.length > 0) {
        startTime = tableInfo.time[0].toLocaleDateString("zh-CN");
        endTime = tableInfo.time[1].toLocaleDateString("zh-CN");
    }
    return request({
        url: '/history_log',
        method: 'post',
        data: {
           user_id: tableInfo.user_id,
           domestic: tableInfo.domestic,
           operation_type: tableInfo.operation_type,
           description: tableInfo.description,
           status: tableInfo.status,
           pageIndex: tableInfo.pageIndex,
           pageSize: tableInfo.pageSize,
           startTime: startTime,
           endTime: endTime,
        }
    });
};


export const versioningQuery = (tableInfo: any) => {
    return request({
        url: '/versioning_query',
        method: 'post',
        data: {
            name: tableInfo.name,
            pageIndex: tableInfo.pageIndex,
            pageSize: tableInfo.pageSize,
        }
    });
};

export const getVersionsByName = (name: string) => {
    return request({
        url: `/versioning_query?name=${name}`,
        method: 'get'
    });
};

export const getLatestVersions = (params: { pageIndex: number, pageSize: number }) => {
    return request({
        url: '/versioning_latest',
        method: 'post',
        data: params
    });
};


export const getAppData = (tableInfo: any) => {
    return request({
        url: '/get_app_data',
        method: 'post',
        data: {
            pageIndex: tableInfo.pageIndex,
            pageSize: tableInfo.pageSize,
            app_store: tableInfo.app_store,
            app_name: tableInfo.app_name,
            package_name: tableInfo.package_name,
            type: tableInfo.type,
            value:tableInfo.value,
            is_new: tableInfo.is_new,
            sortBy: tableInfo.sortBy,
            sortOrder: tableInfo.sortOrder,
            domestic: tableInfo.domestic,
            delete: tableInfo.delete || '0'
        }
    });
};


export const updateAppData = (params: { app_store: string; id: number; data?: any; action?: string; }) => {
    return request({
        url: '/update_app_data',
        method: 'post',
        data: {
            app_store: params.app_store,
            id: params.id,
            data: params.data,
            action: params.action
        }
    });
};

export const SpiderControl = (tableInfo: any) => {
    return request({
        url: '/spider_control',
        method: 'post',
        data: {
            app_stores: tableInfo.app_stores,
            schedule: tableInfo.schedule
        }
    });
};


export const SpiderHistoryQuery = (tableInfo: any) => {
    return request({
        url: '/spider_history_query',
        method: 'post',
        data: {
            pageIndex: tableInfo.pageIndex,
            pageSize: tableInfo.pageSize,
            store: tableInfo.store,
            script_type: tableInfo.script_type,
            status: tableInfo.status,
            sortBy: tableInfo.sortBy,
            sortOrder: tableInfo.sortOrder
        }
    });
};

export const ExportControl = (tableInfo: any) => {
    return request({
        url: '/export_control',
        method: 'post',
        responseType: 'blob',
        timeout: 600000,  // 10分钟超时
        data: {
            app_stores: tableInfo.app_stores
        }
    });
};

export const ApplicationBaseSpider = (tableInfo: any) => {
    return request({
        url: '/application_base_spider',
        method: 'post',
        data: {
            pageIndex: tableInfo.pageIndex || 1,
            pageSize: tableInfo.pageSize || 10,
            appname: tableInfo.appname || '',
            pkg_name: tableInfo.pkg_name || '',
            value: tableInfo.value || '',
            domestic: tableInfo.domestic,  // 必填参数
            sortBy: tableInfo.sortBy || '',
            sortOrder: tableInfo.sortOrder || ''
        }
    });
};

export const StoreConfigQuery = () => {
    return request({
        url: '/store_config',
        method: 'get'
    });
};

export const StoreConfigUpdate = (data: any) => {
    return request({
        url: '/store_config',
        method: 'post',
        data
    });
};

export const StoreConfigRestore = () => {
    return request({
        url: '/store_config',
        method: 'put'
    });
};

export const getCalculateStatus = () => {
    return request({
        url: '/calculate',
        method: 'get'
    });
};

export const startCalculate = (params: { refresh_type: 'immediate' | 'scheduled'; interval?: number }) => {
    return request({
        url: '/calculate',
        method: 'post',
        data: params
    });
};

export const stopCalculate = () => {
    return request({
        url: '/calculate',
        method: 'delete'
    });
};


export const apiPermissionList = () => {
    return request({
        url: '/permission_list',
        method: 'post'
    });
};

export const apiPermissionAdd = (params: any) => {
    return request({
        url: '/permission_add',
        method: 'post',
        data: {
            name: params.name,
            desc: params.desc,
            type: params.type,
            parent_id: params.parent_id
        }
    });
};

export const apiPermissionEdit = (params: any) => {
    return request({
        url: '/permission_edit',
        method: 'post',
        data: {
            id: params.id,
            name: params.name,
            desc: params.desc,
            parent_id: params.parent_id
        }
    });
};

export const apiPermissionDelete = (id: number) => {
    return request({
        url: '/permission_delete',
        method: 'post',
        data: {
            id: id
        }
    });
};

export const apiPermissionAssign = (roleId: number, permissionIds: number[]) => {
    return request({
        url: '/permission_assign',
        method: 'post',
        data: {
            roleId: roleId,
            permissionIds: permissionIds
        }
    });
};

export const apiRolePermissionList = (roleId: number) => {
    return request({
        url: '/role_permission_list',
        method: 'post',
        data: { roleId }
    });
};

export const apiUserPermissionList = () => {
    return request({
        url: '/user_role_permission_list',
        method: 'get'
    });
};
// 获取所有角色
export const apiRoleList = () => {
    return request({
        url: '/role_list',
        method: 'get'
    });
};

// 获取快照类型列表
export const SnapshotTypeList = () => {
    return request({
        url: '/snapshot_type_list',
        method: 'get'
    });
};

// 获取快照对比结果（分页版，支持domestic）
export const SnapshotCompare = (snapshot_type: string, tab: string, page: number, page_size: number, domestic: number, appname: string, value: string) => {
    return request({
        url: '/snapshot_compare',
        method: 'post',
        data: { snapshot_type, tab, page, page_size, domestic, appname, value },
        headers: { 'Content-Type': 'application/json' }
    });
};

// 清理所有快照
export const SnapshotClean = () => {
    return request({
        url: '/snapshot_clean',
        method: 'post'
    });
};

export const getSpiderHistoryLatest = (params: { pageIndex: number, pageSize: number }) => {
    return request({
        url: '/spider_history_latest',
        method: 'post',
        data: params
    });
};

export const getPlatformSupportStats = (params?: any) => {
    return request({
        url: '/sniffer_platform_support_stats',
        method: 'get',
        params
    });
};

export const uploadPcap = (tb: any) => {
    return request({
        url: '/upload_pcap',
        method: 'post',
        headers: { "Content-Type": "multipart/form-data" },
        timeout: 6000000,
        data: {
            file: tb.file.length != 0 ? tb.file[0].raw : null,
            server_ip: tb.server_ip,
            server_username: tb.server_username,
            server_password: tb.server_password,
            server_pcap_path: tb.server_pcap_path,
            name: tb.name,
            value: tb.value,
            platform: tb.platform,
            package: tb.package
        },
    });
};

export const ScriptTable = (tableInfo: any) => {
    let startTime = "";
    let endTime = ""
    if (tableInfo.time != null && tableInfo.time.length > 0) {
        startTime = tableInfo.time[0].toLocaleDateString("zh-CN");
        endTime = tableInfo.time[1].toLocaleDateString("zh-CN");
    }
    return request({
        url: '/script_table',
        method: 'post',
        data: {
           table_type: tableInfo.table_type,
           delete: tableInfo.delete,
           name: tableInfo.name,
           l_class: tableInfo.l_class,
           package: tableInfo.package,
           priority: tableInfo.priority,
           result: tableInfo.result,
           author: tableInfo.author,
           script_type: tableInfo.script_type,
           taskId: tableInfo.taskId,
           pageIndex: tableInfo.pageIndex,
           pageSize: tableInfo.pageSize,
           startTime: startTime,
           endTime: endTime,
           sortBy: tableInfo.sortBy,
           sortOrder: tableInfo.sortOrder,
        }
    });
};

export const StreamRecognitionList = (tableInfo: any) => {
    let startTime = "";
    let endTime = ""
    if (tableInfo.time != null && tableInfo.time.length > 0) {
        startTime = tableInfo.time[0].toLocaleDateString("zh-CN");
        endTime = tableInfo.time[1].toLocaleDateString("zh-CN");
    }
    return request({
        url: '/stream_recognition_list',
        method: 'post',
        data: {
           name: tableInfo.name,
           value: tableInfo.value,
           package: tableInfo.package,
           platform: tableInfo.platform,
           result: tableInfo.result,
           pageIndex: tableInfo.pageIndex,
           pageSize: tableInfo.pageSize,
           startTime: startTime,
           endTime: endTime
        }
    });
};

export const XdrDetailList = (tableInfo: any) => {

    return request({
        url: '/xdr_detail_list',
        method: 'post',
        data: {
           stream_recognition_id: tableInfo.id,
           pageIndex: tableInfo.pageIndex,
           pageSize: tableInfo.pageSize,
        }
    });
};

export const XdrDetailExport = (stream_recognition_id: number) => {
    return request({
        url: '/xdr_detail_list',
        method: 'get',
        params: {
            stream_recognition_id: stream_recognition_id
        }
    });
};

export const XdrDetailDownload = (filename: string) => {
    return request({
        url: '/xdr_detail_export',
        method: 'get',
        params: {
            filename: filename
        },
        responseType: 'blob'
    });
};

// 远程分析相关API
// 获取远程分析状态
export const getRemoteAnalysisStatus = () => {
    return request({
        url: '/remote_analysis',
        method: 'get'
    });
};

// 启动远程分析
export const startRemoteAnalysis = (params: {
    SERVER_IP: string;
    SERVER_USER: string;
    SERVER_PASSWORD: string;
    SERVER_PCAP_PATH: string;
    NET: string;
    SERVER_ANA_IP: string;
    SERVER_ANA_USER: string;
    SERVER_ANA_PASSWORD: string;
    ANA_PATH: string;
    ETH_CONVERT_PATH: string;
}) => {
    return request({
        url: '/remote_analysis',
        method: 'post',
        data: params
    });
};
