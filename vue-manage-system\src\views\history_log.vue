<template>
	<div>
		<div class="container">
			<div class="handle-box">
				<el-select v-model="query.user_id" placeholder="用户" clearable class="handle-select mr10">
					<el-option v-for="item in userList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.domestic" placeholder="国内外" clearable class="handle-select mr10">
					<el-option label="国内" :value="0" />
					<el-option label="国外" :value="1" />
				</el-select>
				<el-select v-model="query.operation_type" placeholder="操作类型" clearable class="handle-select mr10">
					<el-option v-for="op in operationTypes" :key="op.value" :label="op.label" :value="op.value" />
				</el-select>
				<el-input v-model="query.description" placeholder="描述" clearable class="handle-select mr10" />
				<el-select v-model="query.status" placeholder="状态" clearable class="handle-select mr10">
					<el-option label="成功" :value="1" />
					<el-option label="失败" :value="0" />
				</el-select>
                <el-date-picker v-model="query.time" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
					:default-value="[new Date(), new Date()]" class="mr10"/>

				<el-button type="primary" :icon="Search" @click="handleSearch" style="margin-left: 5px;">搜索</el-button>
				<el-button type="warning" :icon="Refresh" @click="handleReset">重置</el-button>
			</div>

			<el-table :data="tableData" border class="table" ref="multipleTable" header-cell-class-name="table-header">
				<el-table-column prop="id" label="ID" width="80" align="center" />
				<el-table-column prop="username" label="用户" align="center" />
				<el-table-column prop="domestic" label="国内外" align="center">
					<template #default="scope">
						<el-tag :type="scope.row.domestic === 0 ? 'primary' : 'info'">
							{{ scope.row.domestic === 0 ? '国内' : '国外' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="operation_type" label="操作类型" align="center">
					<template #default="scope">
						<el-tag :color="getOperationTypeColor(scope.row.operation_type)" style="color: #fff">
							{{ getOperationTypeLabel(scope.row.operation_type) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="description" label="描述" align="center" show-overflow-tooltip/>
				<el-table-column prop="status" label="状态" align="center">
					<template #default="scope">
						<el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
							{{ scope.row.status === 1 ? '成功' : '失败' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="created_at" label="创建时间" align="center" />
				<el-table-column label="操作" width="120" align="center" fixed="right">
					<template #default="scope">
						<el-button type="primary" @click="handleDetail(scope.row)">
							详细信息
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div class="pagination">
				<el-pagination background layout="total, prev, pager, next" :current-page="query.pageIndex"
					:page-size="query.pageSize" :total="pageTotal" @current-change="handlePageChange"></el-pagination>
			</div>
		</div>

		<!-- 详细信息对话框 -->
		<el-dialog v-model="dialogVisible" title="详细信息" width="65%" destroy-on-close>
			<el-descriptions :column="2" border>
				<el-descriptions-item label="用户">{{ currentRow.username }}</el-descriptions-item>
				<el-descriptions-item label="国内外">
					<el-tag :type="currentRow.domestic === 0 ? 'primary' : 'info'">
						{{ currentRow.domestic === 0 ? '国内' : '国外' }}
					</el-tag>
				</el-descriptions-item>
				<el-descriptions-item label="操作类型">
					<el-tag :color="getOperationTypeColor(currentRow.operation_type)" style="color: #fff">
						{{ getOperationTypeLabel(currentRow.operation_type) }}
					</el-tag>
				</el-descriptions-item>
				<el-descriptions-item label="状态">
					<el-tag :type="currentRow.status === 1 ? 'success' : 'danger'">
						{{ currentRow.status === 1 ? '成功' : '失败' }}
					</el-tag>
				</el-descriptions-item>
				<el-descriptions-item label="描述">{{ currentRow.description }}</el-descriptions-item>
				<el-descriptions-item label="创建时间">{{ currentRow.created_at }}</el-descriptions-item>
				<el-descriptions-item label="错误信息" :span="2" v-if="currentRow.error_msg">{{ currentRow.error_msg }}</el-descriptions-item>
			</el-descriptions>

			<!-- 操作数据展示 -->
			<template v-if="currentRow.operation_data">
				<template v-if="currentRow.operation_data.delete">
					<h3 class="common-title">{{ currentRow.operation_data.delete.message }}</h3>
					<el-card class="create-card" shadow="hover">
						<el-descriptions :column="2" border>
							<el-descriptions-item label="应用名称" v-if="currentRow.operation_data.delete.data.name">
								<el-tag type="success">{{ currentRow.operation_data.delete.data.name }}</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="脚本类型" v-if="currentRow.operation_data.delete.data.scriptType">
								<el-tag type="success">{{ currentRow.operation_data.delete.data.scriptType }}</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="小类ID" v-if="currentRow.operation_data.delete.data.value">
								<el-tag type="info">{{ currentRow.operation_data.delete.data.value }}</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="大类名称" v-if="currentRow.operation_data.delete.data.classname">
								<el-tag type="warning">{{ currentRow.operation_data.delete.data.classname }}</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="NTOP" v-if="currentRow.operation_data.delete.data.ntop">
								<el-tag type="primary">{{ currentRow.operation_data.delete.data.ntop }}</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="用户ID" v-if="currentRow.operation_data.delete.data.user_id">
								<el-tag type="primary">{{ currentRow.operation_data.delete.data.user_id }}</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="用户名" v-if="currentRow.operation_data.delete.data.username">
								<el-tag type="success">{{ currentRow.operation_data.delete.data.username }}</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="角色" v-if="currentRow.operation_data.delete.data.role">
								<el-tag type="warning">{{ roleNameMap[currentRow.operation_data.delete.data.role] || currentRow.operation_data.delete.data.role }}</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="状态" v-if="currentRow.operation_data.delete.data.activity">
								<el-tag :type="currentRow.operation_data.delete.data.activity === '激活' ? 'success' : 'danger'">
									{{ currentRow.operation_data.delete.data.activity }}
								</el-tag>
							</el-descriptions-item>
						</el-descriptions>
					</el-card>
				</template>
				<template v-if="currentRow.operation_data.create">
					<h3 class="common-title">{{ currentRow.operation_data.create.message }}</h3>
					<el-card class="create-card" shadow="hover">
						<!-- 应用新增 -->
						<template v-if="currentRow.operation_data.create.data.name && currentRow.operation_data.create.data.value">
						<el-descriptions :column="4" border>
								<el-descriptions-item label="应用名称" v-if="currentRow.operation_data.create.data.name">
								<el-tag type="success">{{ currentRow.operation_data.create.data.name }}</el-tag>
							</el-descriptions-item>
								<el-descriptions-item label="小类ID" v-if="currentRow.operation_data.create.data.value">
								<el-tag type="info">{{ currentRow.operation_data.create.data.value }}</el-tag>
							</el-descriptions-item>
								<el-descriptions-item label="大类名称" v-if="currentRow.operation_data.create.data.classname">
								<el-tag type="warning">{{ currentRow.operation_data.create.data.classname }}</el-tag>
							</el-descriptions-item>
								<el-descriptions-item label="NTOP" v-if="currentRow.operation_data.create.data.ntop">
								<el-tag type="primary">{{ currentRow.operation_data.create.data.ntop }}</el-tag>
							</el-descriptions-item>
						</el-descriptions>
						<div class="xml-preview">
							<pre class="xml-content"><code>{{ buildSnifferXml(currentRow.operation_data.create.data) }}</code></pre>
						</div>
						</template>
						<!-- 用户新增 -->
						<template v-if="currentRow.operation_data.create.data.user_id && currentRow.operation_data.create.data.username">
							<el-descriptions :column="3" border>
								<el-descriptions-item label="用户ID">
									<el-tag type="primary">{{ currentRow.operation_data.create.data.user_id }}</el-tag>
								</el-descriptions-item>
								<el-descriptions-item label="用户名">
									<el-tag type="success">{{ currentRow.operation_data.create.data.username }}</el-tag>
								</el-descriptions-item>
								<el-descriptions-item label="角色">
									<el-tag type="warning">{{ roleNameMap[currentRow.operation_data.create.data.role] || currentRow.operation_data.create.data.role }}</el-tag>
								</el-descriptions-item>
								<el-descriptions-item label="角色ID">
									<el-tag type="info">{{ currentRow.operation_data.create.data.role_id }}</el-tag>
								</el-descriptions-item>
								<el-descriptions-item label="状态">
									<el-tag :type="currentRow.operation_data.create.data.activity === 1 ? 'success' : 'danger'">
										{{ currentRow.operation_data.create.data.activity === 1 ? '激活' : '冻结' }}
									</el-tag>
								</el-descriptions-item>
							</el-descriptions>
						</template>
					</el-card>
				</template>
				<template v-if="currentRow.operation_data.user_activity">
					<h3 class="common-title">{{ currentRow.operation_data.user_activity.message }}</h3>
					<el-card class="create-card" shadow="hover">
						<el-descriptions :column="3" border>
							<el-descriptions-item label="用户ID">
								<el-tag type="primary">{{ currentRow.operation_data.user_activity.data.user_id }}</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="用户名">
								<el-tag type="success">{{ currentRow.operation_data.user_activity.data.username }}</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="状态变更">
								<el-tag type="info">{{ currentRow.operation_data.user_activity.data.old_status }}</el-tag>
								<span style="margin: 0 8px;">→</span>
								<el-tag :type="currentRow.operation_data.user_activity.data.new_status === '激活' ? 'success' : 'danger'">
									{{ currentRow.operation_data.user_activity.data.new_status }}
								</el-tag>
							</el-descriptions-item>
						</el-descriptions>
					</el-card>
				</template>
				<template v-if="currentRow.operation_data.permission_assign">
					<h3 class="common-title">{{ currentRow.operation_data.permission_assign.message }}</h3>
					<el-card class="create-card" shadow="hover">
						<el-descriptions :column="2" border>
							<el-descriptions-item label="角色ID">
								<el-tag type="primary">{{ currentRow.operation_data.permission_assign.data.role_id }}</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="角色名称">
								<el-tag type="success">{{ roleNameMap[currentRow.operation_data.permission_assign.data.role_name] || currentRow.operation_data.permission_assign.data.role_name }}</el-tag>
							</el-descriptions-item>
						</el-descriptions>
						<el-row :gutter="0" style="margin-top: 20px; align-items: stretch;">
							<el-col :span="11">
								<div class="permission-title">分配前权限</div>
								<div v-if="currentRow.operation_data.permission_assign.data.old_permissions && currentRow.operation_data.permission_assign.data.old_permissions.length > 0">
									<span
										v-for="permission in currentRow.operation_data.permission_assign.data.old_permissions"
										:key="permission"
										class="permission-tag info"
									>{{ permission }}</span>
								</div>
								<div v-else style="color: #999; font-style: italic;">无权限</div>
							</el-col>
							<div class="permission-divider"></div>
							<el-col :span="11">
								<div class="permission-title">分配后权限</div>
								<div v-if="currentRow.operation_data.permission_assign.data.new_permissions && currentRow.operation_data.permission_assign.data.new_permissions.length > 0">
									<span
										v-for="permission in currentRow.operation_data.permission_assign.data.new_permissions"
										:key="permission"
										class="permission-tag success"
									>{{ permission }}</span>
								</div>
								<div v-else style="color: #999; font-style: italic;">无权限</div>
							</el-col>
						</el-row>
					</el-card>
				</template>
				<template v-if="currentRow.operation_data.changes">
					<h3 class="common-title">{{ currentRow.operation_data.changes.message }}</h3>
					<el-card class="create-card" shadow="hover">
						<el-row :gutter="20">
							<el-col :span="12">
								<div class="change-title">变更前</div>
								<el-descriptions :column="2" border>
									<el-descriptions-item label="应用名称">
										<el-tag type="primary">{{ currentRow.operation_data.changes.data.old.name }}</el-tag>
									</el-descriptions-item>
									<el-descriptions-item label="小类ID">
										<el-tag type="primary">{{ currentRow.operation_data.changes.data.old.value }}</el-tag>
									</el-descriptions-item>
									<el-descriptions-item label="大类名称">
										<el-tag type="primary">{{ currentRow.operation_data.changes.data.old.classname }}</el-tag>
									</el-descriptions-item>
									<el-descriptions-item label="NTOP">
										<el-tag type="primary">{{ currentRow.operation_data.changes.data.old.ntop }}</el-tag>
									</el-descriptions-item>
								</el-descriptions>
							</el-col>
							<el-col :span="12">
								<div class="change-title">变更后</div>
								<el-descriptions :column="2" border>
									<el-descriptions-item label="应用名称">
										<el-tag 
											:type="currentRow.operation_data.changes.data.old.name !== currentRow.operation_data.changes.data.new.name ? 'success' : 'primary'"
										>{{ currentRow.operation_data.changes.data.new.name }}</el-tag>
									</el-descriptions-item>
									<el-descriptions-item label="小类ID">
										<el-tag 
											:type="Number(currentRow.operation_data.changes.data.old.value) !== Number(currentRow.operation_data.changes.data.new.value) ? 'success' : 'primary'"
										>{{ currentRow.operation_data.changes.data.new.value }}</el-tag>
									</el-descriptions-item>
									<el-descriptions-item label="大类名称">
										<el-tag 
											:type="currentRow.operation_data.changes.data.old.classname !== currentRow.operation_data.changes.data.new.classname ? 'success' : 'primary'"
										>{{ currentRow.operation_data.changes.data.new.classname }}</el-tag>
									</el-descriptions-item>
									<el-descriptions-item label="NTOP">
										<el-tag 
											:type="currentRow.operation_data.changes.data.old.ntop !== currentRow.operation_data.changes.data.new.ntop ? 'success' : 'primary'"
										>{{ currentRow.operation_data.changes.data.new.ntop }}</el-tag>
									</el-descriptions-item>
								</el-descriptions>
							</el-col>
						</el-row>
						<div class="change-title" style="margin-top: 20px;text-align: center;">详细变更</div>
						<div class="xml-preview">
							<pre class="xml-content" v-html="buildDiffXml(currentRow.operation_data.changes.data.old, currentRow.operation_data.changes.data.new)"></pre>
						</div>
					</el-card>
				</template>
				<template v-if="currentRow.operation_data.export">
					<h3 class="common-title">{{ currentRow.operation_data.export.message }}</h3>
					<el-card class="create-card" shadow="hover">
						<el-descriptions border>
							<el-descriptions-item label="下载地址" v-if="currentRow.operation_data.export.data.download_url">
								<el-button
									type="primary"
									size="small"
									:icon="Download"
									@click="handleDownload(currentRow.operation_data.export.data.download_url)"
								>
									点击下载
								</el-button>
							</el-descriptions-item>
						</el-descriptions>
					</el-card>
				</template>
				<template v-if="currentRow.operation_data.imports">
					<h3 class="common-title">{{ currentRow.operation_data.imports.message }}</h3>
					<el-card class="create-card" shadow="hover">
						<el-descriptions border>
							<el-descriptions-item label="下载地址" v-if="currentRow.operation_data.imports.data.download_url">
								<el-button
									type="primary"
									size="small"
									:icon="Download"
									@click="handleDownload(currentRow.operation_data.imports.data.download_url)"
								>
									点击下载
								</el-button>
							</el-descriptions-item>
						</el-descriptions>
					</el-card>
				</template>
				<template v-if="currentRow.operation_data.publish">
					<h3 class="common-title">{{ currentRow.operation_data.publish.data.project }} -- {{ currentRow.operation_data.publish.message }}</h3>
					<el-card class="publish-card" shadow="hover">
						<el-descriptions :column="3" border>
							<el-descriptions-item label="项目名">
								<el-tag size="small" type="success">
									{{ currentRow.operation_data.publish.data.project }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="版本" v-if="currentRow.operation_data?.publish?.data?.version">
								<el-tag size="small" effect="plain">
									{{ currentRow.operation_data.publish.data.version }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="SDK版本" v-if="currentRow.operation_data?.publish?.data?.sdk_version">
								<el-tag size="small" type="warning" effect="plain">
									{{ currentRow.operation_data.publish.data.sdk_version }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="运营商" v-if="currentRow.operation_data?.publish?.data?.operator">
								<el-tag :color="getOperatorColor(currentRow.operation_data.publish.data.operator)" style="color: #fff" round>
									{{ getOperatorLabel(currentRow.operation_data.publish.data.operator) }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="QOE标记" v-if="currentRow.operation_data?.publish?.data?.is_qoe !== undefined">
								<el-tag :type="currentRow.operation_data.publish.data.is_qoe ? 'success' : 'info'" effect="light" round>
									{{ currentRow.operation_data.publish.data.is_qoe ? '标记' : '不标记' }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="P2P标记" v-if="currentRow.operation_data?.publish?.data?.is_p2p !== undefined">
								<el-tag :type="currentRow.operation_data.publish.data.is_p2p ? 'success' : 'info'" effect="light" round>
									{{ currentRow.operation_data.publish.data.is_p2p ? '标记' : '不标记' }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="大小类" v-if="currentRow.operation_data?.publish?.data?.op_flag !== undefined">
								<el-tag :type="currentRow.operation_data.publish.data.op_flag ? 'success' : 'info'" effect="light" round>
									{{ currentRow.operation_data.publish.data.op_flag ? "转换" : "不转换" }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="裁剪数量" v-if="currentRow.operation_data?.publish?.data?.split_top">
								<el-tag :type="currentRow.operation_data.publish.data.split_top === 0 ? 'info' : 'warning'" effect="light" round>
									{{ currentRow.operation_data.publish.data.split_top === 0 ? "未裁" : "裁" + currentRow.operation_data.publish.data.split_top }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="配置信息" v-if="currentRow.operation_data?.publish?.data?.c_flag !== undefined">
								<el-tag :type="currentRow.operation_data.publish.data.c_flag ? 'primary' : 'success'" effect="light" round>
									{{ currentRow.operation_data.publish.data.c_flag ? "主线版本" : "集采版本" }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="下载地址" v-if="currentRow.operation_data?.publish?.data?.download_url">
								<el-button
									type="primary"
									size="small"
									:icon="Download"
									@click="handleDownload(currentRow.operation_data.publish.data.download_url)"
								>
									点击下载
								</el-button>
							</el-descriptions-item>
						</el-descriptions>
					</el-card>
				</template>
				<template v-if="currentRow.operation_data.upload">
					<h3 class="common-title">{{ currentRow.operation_data.upload.message }}</h3>
					<el-card class="publish-card" shadow="hover">
						<el-descriptions :column="3" border>
							<el-descriptions-item label="脚本类型">
								<el-tag size="small" type="success">
									{{ currentRow.operation_data.upload.data.project }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="作者">
								<el-tag size="small" type="warning">
									{{ currentRow.operation_data.upload.data.author }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="脚本地址">
								<el-button
									type="primary"
									size="small"
									:icon="Download"
									@click="handleDownload(currentRow.operation_data.upload.data.download_url)"
								>
									点击下载
								</el-button>
							</el-descriptions-item>
						</el-descriptions>
					</el-card>
				</template>
				<template v-if="currentRow.operation_data.analyse">
					<h3 class="common-title">{{ currentRow.operation_data.analyse.message }}</h3>
					<el-card class="publish-card" shadow="hover">
						<el-descriptions :column="3" border>
							<el-descriptions-item label="脚本类型">
								<el-tag size="small" type="primary">
									{{ currentRow.operation_data.analyse.data.scriptType }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="脚本名称">
								<el-tag size="small" type="success">
									{{ currentRow.operation_data.analyse.data.name }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="作者">
								<el-tag size="small" type="info">
									{{ currentRow.operation_data.analyse.data.author }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="分析原因" v-if="currentRow.operation_data.analyse.data.self_reason">
								<el-tag size="small" type="warning">
									{{ currentRow.operation_data.analyse.data.self_reason }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="修改方式" v-if="currentRow.operation_data.analyse.data.measure">
								<el-tag size="small" type="warning">
									{{ currentRow.operation_data.analyse.data.measure }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="执行方式">
								<el-tag size="small" :type="currentRow.operation_data.analyse.data.saveOnly === 1 ? 'info' : 'success'">
									{{ currentRow.operation_data.analyse.data.saveOnly === 1 ? '仅保存' : '保存并运行' }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="应用来源" v-if="currentRow.operation_data.analyse.data.store">
								<el-tag size="small" type="warning">
									{{ currentRow.operation_data.analyse.data.store }}
								</el-tag>
							</el-descriptions-item>
							<el-descriptions-item label="指派作者" v-if="currentRow.operation_data.analyse.data.assigned_author">
								<el-tag size="small" type="warning">
									{{ currentRow.operation_data.analyse.data.assigned_author }}
								</el-tag>
							</el-descriptions-item>
						</el-descriptions>
					</el-card>
				</template>
				<template v-if="currentRow.operation_data.execute">
					<h3 class="common-title">{{ currentRow.operation_data.execute.message }}</h3>
					<el-card class="publish-card" shadow="hover">
						<el-descriptions :column="2" border>
							<el-descriptions-item label="应用商店">
								<template v-if="Array.isArray(currentRow.operation_data.execute.data.stores)">
									<el-tag 
										v-for="store in currentRow.operation_data.execute.data.stores" 
										:key="store"
										size="small" 
										type="primary"
										class="mr10"
									>
										{{ getStoreName(store) }}
									</el-tag>
								</template>
							</el-descriptions-item>
							<el-descriptions-item label="定时时间" v-if="currentRow.operation_data.execute.data.schedule">
								<el-tag size="small" type="success" class="mr10">
									{{ currentRow.operation_data.execute.data.schedule.type === 'daily' ? '每天' : currentRow.operation_data.execute.data.schedule.type }}
								</el-tag>
								<el-tag size="small" type="info">
									{{ currentRow.operation_data.execute.data.schedule.time }}
								</el-tag>
							</el-descriptions-item>
						</el-descriptions>
					</el-card>
				</template>
			</template>
			<template #footer>
				<el-button @click="dialogVisible = false">关闭</el-button>
				<el-button
					v-if="currentRow.operation_type === 'app_update'"
					type="danger"
					@click="handleRollback(currentRow)"
				>
					回退
				</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="history_log">
import { ref, reactive, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh, Download } from '@element-plus/icons-vue';
import { historyLog, getAuthors, snfApplicationAdd, apiUserPermissionList } from '../api/index';

interface TableItem {
	id: number;
	user_id: number;
	username: string;
	domestic: number;
	operation_type: string;
	description: string;
	status: number;
	error_msg: string;
	operation_data: any;
	created_at: string;
}

const query = reactive({
	user_id: '',
	domestic: '',
	operation_type: '',
	description: '',
	status: '',
	time: [],
	pageIndex: 1,
	pageSize: 10
});

const tableData = ref<TableItem[]>([]);
const pageTotal = ref(0);
const userList = ref([]);
const dialogVisible = ref(false);
const currentRow = ref<TableItem>({} as TableItem);

const colorPool = [
  '#9C27B0', '#FF9800', '#795548', '#607D8B', '#4CAF50', '#2196F3', '#CDDC39', '#3F51B5',
  '#00BCD4', '#009688', '#673AB7', '#FF5722', '#E91E63', '#8BC34A', '#9E9E9E', '#FFC107', '#FF5252'
];
const operationTypes = ref<any[]>([]);

const loadOperationTypes = async () => {
  try {
    const res = await apiUserPermissionList();
    operationTypes.value = res.data.permissionList.map((item: any, idx: number) => ({
      value: item.name,   // 英文唯一标识
      label: item.desc,   // 中文显示
      color: colorPool[idx % colorPool.length]
    }));
  } catch (err) {
    operationTypes.value = [];
  }
};

const operatorTypes = [
	{ value: 'cucc', label: '联通', color: '#FF6B6B' },      // 珊瑚红
	{ value: 'cmcc', label: '移动', color: '#4ECDC4' },      // 薄荷绿
	{ value: 'ctcc', label: '电信', color: '#45B7D1' },      // 天青色
	{ value: 'brd', label: 'brd', color: '#96CEB4' }         // 淡绿色
];

const roleNameMap: Record<string, string> = {
	'Administrator': '超级管理员',
	'Author': '普通用户',
	'Analyst': '协议分析师'
};

const appStores = [
	{ label: '华为应用商店', value: 'huawei' },
	{ label: '百度手机助手', value: 'baidu' },
	{ label: '小米应用商店', value: 'xiaomi' },
	{ label: '应用宝', value: 'tencent' },
	{ label: 'Google Play', value: 'google_play' },
	{ label: 'App Store', value: 'app_store' },
	{ label: '微软应用商店', value: 'microsoft' },
	{ label: '360宝库', value: 'baoku' },
	{ label: 'WEB网页', value: 'web_ranking' }
];

const getStoreName = (value: string) => {
	const store = appStores.find(item => item.value === value);
	return store ? store.label : value;
};

// 获取表格数据
const getData = async () => {
	try {
		const res = await historyLog(query);
		tableData.value = res.data.list;
		pageTotal.value = res.data.total;
	} catch (error) {
		ElMessage.error('获取日志数据异常！');
	}
};

// 查询操作
const handleSearch = () => {
	query.pageIndex = 1;
	getData();
};

// 重置查询
const handleReset = () => {
	query.user_id = '';
	query.domestic = '';
	query.operation_type = '';
	query.description = '';
	query.status = '';
	query.time = [];
	query.pageIndex = 1;
	getData();
};

// 分页导航
const handlePageChange = (val: number) => {
	query.pageIndex = val;
	getData();
};

// 获取用户列表
const getUserList = async () => {
	const res = await getAuthors();
	userList.value = res.data;
};

// 查看详细信息
const handleDetail = (row: TableItem) => {
	currentRow.value = row;
	dialogVisible.value = true;
};

// 获取操作类型颜色
const getOperationTypeColor = (type: string) => {
  if (!type) return '';
  const cleanType = type.trim().toUpperCase();
  const op = operationTypes.value.find((op: any) => op.value === cleanType);
  return op?.color || '#909399';
};

// 获取操作类型显示文本
const getOperationTypeLabel = (type: string) => {
  if (!type) return '';
  const cleanType = type.trim().toUpperCase();
  const op = operationTypes.value.find((op: any) => op.value === cleanType);
  return op ? op.label : type;
};

const handleDownload = async (url: string) => {
	try {
		const response = await fetch(url);
		if (!response.ok) {
			const errorData = await response.json();
			ElMessage.error(errorData.message || '下载失败');
			return;
		}

		const blob = await response.blob();
		const downloadUrl = window.URL.createObjectURL(blob);
		const link = document.createElement('a');
		link.href = downloadUrl;
		link.style.display = 'none';

		const contentDisposition = response.headers.get('Content-Disposition');
		const fileName = contentDisposition
			? decodeURIComponent(contentDisposition.split('filename=')[1]?.replace(/"/g, ''))
			: '下载文件';
		link.download = fileName;
		
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		
		// 清理URL对象
		window.URL.revokeObjectURL(downloadUrl);
		
		ElMessage.success('下载成功');
	} catch (error) {
		console.error('下载出错:', error);
		ElMessage.error('下载过程中出现错误，请稍后重试');
	}
};

// 获取运营商颜色
const getOperatorColor = (operator: string) => {
	const op = operatorTypes.find(op => op.value === operator?.toLowerCase());
	return op?.color || '#909399';
};

// 获取运营商显示文本
const getOperatorLabel = (operator: string) => {
	const op = operatorTypes.find(op => op.value === operator?.toLowerCase());
	return op?.label || operator;
};

// 构建应用XML字符串
const buildSnifferXml = (data: any) => {
	if (!data) return '';
	
	// 构建sniffer标签的属性
	const snifferAttrs = `classname="${data.classname}" class="${data.class_id}" servertype="${data.servertype}" name="${data.name}" value="${data.value}" enname="${data.enname}" enclassname="${data.enclassname}"`;
	
	// 构建各种字符串标签
	let xmlParts = [];
	
	// 处理knports
	if (data.knports?.length > 0) {
		xmlParts.push(data.knports.map((item: any) => {
			const attrs = [];
			if (item.port) attrs.push(`port="${item.port}"`);
			if (item.proto) attrs.push(`proto="${item.proto}"`);
			if (item.isreal) attrs.push(`isreal="${item.isreal}"`);
			return `    <knport ${attrs.join(' ')} />`;
		}).join('\n'));
	}
	
	// 处理ipports
	if (data.ipports?.length > 0) {
		xmlParts.push(data.ipports.map((item: any) => {
			const attrs = [];
			if (item.ip) attrs.push(`ip="${item.ip}"`);
			if (item.port) attrs.push(`port="${item.port}"`);
			if (item.proto) attrs.push(`proto="${item.proto}"`);
			return `    <ipport ${attrs.join(' ')} />`;
		}).join('\n'));
	}
	
	// 处理varkeys
	if (data.varkeys?.length > 0) {
		xmlParts.push(data.varkeys.map((item: any) => {
			const attrs = [];
			if (item.subsection) attrs.push(`subsection="${item.subsection}"`);
			if (item.sub_name) attrs.push(`sub_name="${item.sub_name}"`);
			if (item.proto) attrs.push(`proto="${item.proto}"`);
			if (item.video_type) attrs.push(`video_type="${item.video_type}"`);
			return `    <varkey ${attrs.join(' ')}>${item.value}</varkey>`;
		}).join('\n'));
	}
	
	// 处理普通字符串
	if (data.strings?.length > 0) {
		xmlParts.push(data.strings.map((item: any) => {
			const attrs = [];
			if (item.subsection) attrs.push(`subsection="${item.subsection}"`);
			if (item.sub_name) attrs.push(`sub_name="${item.sub_name}"`);
			if (item.source) attrs.push(`source="${item.source}"`);
			if (item.video_type) attrs.push(`video_type="${item.video_type}"`);
			return `    <string ${attrs.join(' ')}>${item.value}</string>`;
		}).join('\n'));
	}
	
	// 处理CT字符串
	if (data.ctstrings?.length > 0) {
		xmlParts.push(data.ctstrings.map((item: any) => {
			const attrs = [];
			if (item.subsection) attrs.push(`subsection="${item.subsection}"`);
			if (item.sub_name) attrs.push(`sub_name="${item.sub_name}"`);
			if (item.video_type) attrs.push(`video_type="${item.video_type}"`);
			return `    <ct_string ${attrs.length > 0 ? attrs.join(' ') + ' ' : ''}>${item.value}</ct_string>`;
		}).join('\n'));
	}
	
	// 处理UA字符串
	if (data.uastrings?.length > 0) {
		xmlParts.push(data.uastrings.map((item: any) => {
			const attrs = [];
			if (item.subsection) attrs.push(`subsection="${item.subsection}"`);
			if (item.sub_name) attrs.push(`sub_name="${item.sub_name}"`);
			if (item.video_type) attrs.push(`video_type="${item.video_type}"`);
			return `    <ua_string ${attrs.length > 0 ? attrs.join(' ') + ' ' : ''}>${item.value}</ua_string>`;
		}).join('\n'));
	}
	
	// 处理RF字符串
	if (data.rfstrings?.length > 0) {
		xmlParts.push(data.rfstrings.map((item: any) => {
			const attrs = [];
			if (item.subsection) attrs.push(`subsection="${item.subsection}"`);
			if (item.sub_name) attrs.push(`sub_name="${item.sub_name}"`);
			if (item.video_type) attrs.push(`video_type="${item.video_type}"`);
			return `    <rf_string ${attrs.length > 0 ? attrs.join(' ') + ' ' : ''}>${item.value}</rf_string>`;
		}).join('\n'));
	}
	
	// 处理Payload字符串
	if (data.payloadstrings?.length > 0) {
		xmlParts.push(data.payloadstrings.map((item: any) => {
			const attrs = [];
			if (item.subsection) attrs.push(`subsection="${item.subsection}"`);
			if (item.sub_name) attrs.push(`sub_name="${item.sub_name}"`);
			if (item.video_type) attrs.push(`video_type="${item.video_type}"`);
			if (item.proto) attrs.push(`proto="${item.proto}"`);
			if (item.isbegin) attrs.push(`isbegin="${item.isbegin}"`);
			return `    <payload_string ${attrs.length > 0 ? attrs.join(' ') + ' ' : ''}>${item.value}</payload_string>`;
		}).join('\n'));
	}
	
	// 组合最终的XML
	const xmlContent = xmlParts.filter(part => part).join('\n');
	return `<sniffer ${snifferAttrs}>\n${xmlContent}\n</sniffer>`;
};

// 构建差异化的XML显示
const buildDiffXml = (oldData: any, newData: any) => {
	if (!oldData || !newData) return '';
	
	const escapeHtml = (str: string) => {
		return str.replace(/[&<>]/g, (tag) => ({
			'&': '&amp;',
			'<': '&lt;',
			'>': '&gt;'
		}[tag] || tag));
	};
	
	const oldXml = buildSnifferXml(oldData).split('\n');
	const newXml = buildSnifferXml(newData).split('\n');
	
	let result = [];
	let i = 0, j = 0;
	
	while (i < oldXml.length || j < newXml.length) {
		if (i >= oldXml.length) {
			// 剩余的新行都是新增的
			result.push(`<span style="color: green;">+ ${escapeHtml(newXml[j])}</span>`);
			j++;
		} else if (j >= newXml.length) {
			// 剩余的旧行都是删除的
			result.push(`<span style="color: red;">- ${escapeHtml(oldXml[i])}</span>`);
			i++;
		} else if (oldXml[i] === newXml[j]) {
			// 相同的行保持不变
			result.push(escapeHtml(oldXml[i]));
			i++;
			j++;
		} else {
			// 找到最长的相同子序列
			let found = false;
			for (let k = 1; k < 3; k++) {
				if (j + k < newXml.length && oldXml[i] === newXml[j + k]) {
					// 新增了k行
					for (let m = 0; m < k; m++) {
						result.push(`<span style="color: green;">+ ${escapeHtml(newXml[j + m])}</span>`);
					}
					result.push(escapeHtml(oldXml[i]));
					i++;
					j += k + 1;
					found = true;
					break;
				} else if (i + k < oldXml.length && oldXml[i + k] === newXml[j]) {
					// 删除了k行
					for (let m = 0; m < k; m++) {
						result.push(`<span style="color: red;">- ${escapeHtml(oldXml[i + m])}</span>`);
					}
					result.push(escapeHtml(newXml[j]));
					i += k + 1;
					j++;
					found = true;
					break;
				}
			}
			if (!found) {
				// 如果没找到匹配,就标记为修改
				result.push(`<span style="color: red;">- ${escapeHtml(oldXml[i])}</span>`);
				result.push(`<span style="color: green;">+ ${escapeHtml(newXml[j])}</span>`);
				i++;
				j++;
			}
		}
	}
	
	return result.join('\n');
};

const handleRollback = async (row: any) => {
	const oldData = row.operation_data?.changes?.data?.old || row.operation_data?.data?.old;
	const newData = row.operation_data?.changes?.data?.new || row.operation_data?.data?.new;
	if (!oldData || !newData) {
		ElMessage.error('没有找到可回退的数据');
		return;
	}
	oldData.id = newData.id;

	try {
		await ElMessageBox.confirm('确定要回退到该操作前的状态吗？', '回退确认', { type: 'warning' });
	} catch {
		return;
	}

	try {
		const res = await snfApplicationAdd(oldData, 3); 
		if (res.data.code === 200) {
		ElMessage.success('回退成功');
		getData();
		} else {
		ElMessage.error(res.data.message || '回退失败');
		}
	} catch (e) {
		ElMessage.error('回退请求异常');
	}
};

onActivated(() => {
	loadOperationTypes();
	getUserList();
	getData();
});

</script>

<style scoped>
.handle-box {
	margin-bottom: 20px;
	align-items: center;
}

.handle-select {
	width: 100px;
}

.handle-input {
	width: 150px;
}

.table {
	width: 100%;
	font-size: 14px;
}

.mr10 {
	margin-right: 10px;
}

.table-td-thumb {
	display: block;
	margin: auto;
	width: 40px;
	height: 40px;
}

.pagination {
	margin: 20px 0;
	text-align: right;
}

/* 优化详细信息对话框样式 */
:deep(.el-dialog) {
	border-radius: 8px;
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
	margin-top: 5vh;
}

:deep(.el-dialog__header) {
	margin: 0;
	padding: 20px 24px;
	border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-dialog__title) {
	font-size: 18px;
	font-weight: 600;
	color: var(--el-text-color-primary);
}

:deep(.el-dialog__body) {
	padding: 24px;
}

:deep(.el-descriptions) {
	padding: 16px;
	background: var(--el-bg-color-page);
	border-radius: 8px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

:deep(.el-descriptions__title) {
	margin-bottom: 16px;
	font-size: 16px;
	font-weight: 600;
}

:deep(.el-descriptions__label) {
	font-weight: 500;
	color: var(--el-text-color-regular);
	background-color: var(--el-fill-color-light);
	min-width: 150px;
}

:deep(.el-descriptions__content) {
	padding: 12px 16px;
}

:deep(.el-tag) {
	font-weight: 500;
	height: 28px;
	line-height: 28px;
	padding: 0 12px;
}

pre {
	background-color: var(--el-fill-color-lighter);
	padding: 16px;
	border-radius: 8px;
	max-height: 400px;
	overflow: auto;
	margin: 0;
	white-space: pre-wrap;
	word-wrap: break-word;
	font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
	font-size: 13px;
	line-height: 1.6;
	color: var(--el-text-color-primary);
	border: 1px solid var(--el-border-color-lighter);
}

.common-title {
	margin: 24px 0 16px;
	padding-bottom: 12px;
	color: var(--el-text-color-primary);
	font-size: 16px;
	font-weight: 600;
	text-align: center;
	border-bottom: 1px solid var(--el-border-color-lighter);
}

.create-card,
.publish-card {
	margin-bottom: 24px;
	border-radius: 8px;
	overflow: hidden;
	transition: all 0.3s ease;
}

.create-card:hover,
.publish-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
	padding: 20px;
}

.xml-preview {
	margin-top: 24px;
	padding: 20px;
	background-color: var(--el-bg-color);
	border-radius: 8px;
	border: 1px solid var(--el-border-color-light);
}

.xml-content {
	margin: 0;
	padding: 0;
	white-space: pre-wrap;
	font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
	font-size: 13px;
	line-height: 1.6;
	color: var(--el-text-color-primary);
}

.xml-content :deep(code) {
	color: #476582;
}

.change-title {
	font-size: 15px;
	font-weight: 600;
	color: var(--el-text-color-primary);
	margin: 16px 0 12px;
	padding: 8px 0;
	border-bottom: 2px solid var(--el-border-color-light);
}

/* 差异对比样式优化 */
.xml-content :deep(span[style*="color: red"]) {
	background-color: rgba(255, 0, 0, 0.05);
	display: block;
	border-radius: 4px;
}

.xml-content :deep(span[style*="color: green"]) {
	background-color: rgba(0, 255, 0, 0.05);
	display: block;
	border-radius: 4px;
}

/* 按钮样式优化 */
:deep(.el-button--primary) {
	font-weight: 500;
	padding: 8px 16px;
	height: 32px;
	line-height: 1;
	border-radius: 6px;
}

:deep(.el-button--primary:hover) {
	transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 描述列表间距优化 */
:deep(.el-descriptions__body .el-descriptions__table) {
	border-radius: 8px;
	overflow: hidden;
}

:deep(.el-descriptions__cell) {
	padding: 16px;
}

/* 标签组样式优化 */
:deep(.el-tag + .el-tag) {
	margin-left: 8px;
}

:deep(.el-tag.is-round) {
	border-radius: 16px;
}

.permission-tag {
  border-radius: 16px;
  padding: 4px 16px;
  font-size: 15px;
  margin-bottom: 10px;
  margin-right: 10px;
  font-weight: 500;
  transition: box-shadow 0.2s;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  display: inline-block;
}
.permission-tag.info {
  background: #f4f4f5;
  color: #909399;
  border: 1px solid #e4e7ed;
}
.permission-tag.success {
  background: #e8f5e9;
  color: #43a047;
  border: 1px solid #b2dfdb;
}
.permission-tag:hover {
  box-shadow: 0 2px 8px rgba(67,160,71,0.15);
  cursor: pointer;
}
.permission-divider {
  border-left: 2px solid #ebeef5;
  height: 100%;
  margin: 0 24px;
  min-height: 80px;
  display: inline-block;
}
.permission-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
}
</style>
