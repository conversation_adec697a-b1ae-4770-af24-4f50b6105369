<template>
    <div>
        <div class="container">
            <div class="search-box">
                <el-select
                    v-model="query.delete"
                    placeholder="状态"
                    class="search-input mr10"
                    @change="handleSearch"
                    >
                    <el-option
                        v-for="item in delete_options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                    />
                </el-select>
                <el-select
                    v-model="query.table_type"
                    placeholder="平台"
                    class="search-input mr10"
                    @change="handleSearch"
                    >
                    <el-option
                        v-for="item in platformOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
                <el-input v-model="query.name" placeholder="应用名" class="search-input mr10" clearable @change="handleSearch"></el-input>
                <el-input v-model="query.l_class" placeholder="小类ID" class="search-input mr10" clearable @change="handleSearch"></el-input>
                <el-input v-model="query.package" placeholder="包名" class="search-input mr10" clearable @change="handleSearch"></el-input>
                <el-select
                    v-model="query.priority"
                    clearable
                    placeholder="优先级"
                    class="search-input mr10"
                    @change="handleSearch"
                    >
                    <el-option
                        v-for="item in status_options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                    />
                </el-select>
                <el-select
                    v-model="query.result"
                    clearable
                    placeholder="运行结果"
                    class="search-input mr10"
                    @change="handleSearch"
                    >
                    <el-option
                        v-for="item in result_options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                    />
                </el-select>
                <el-select
                    v-model="query.author"
                    v-if="author_permiss"
                    clearable
                    remote
                    placeholder="作者"
                    class="search-input mr10"
                    filterable
                    reserve-keyword
                    remote-show-suffix
                    @change="handleSearch"
                    >
                    <el-option
                        v-for="item in authors_options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                    />
                </el-select>
                <el-select
                    v-model="query.script_type"
                    clearable
                    placeholder="脚本类型"
                    class="search-input mr10"
                    @change="handleSearch"
                    >
                    <el-option
                        v-for="item in scriptTypeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                    />
                </el-select>
                <el-date-picker
                    v-model="query.time"
                    type="daterange"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-value="[new Date(), new Date()]"
                    @change="handleSearch"
                />
            </div>
            <div class="search-box">
                <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
                <el-button type="warning" :icon="CirclePlusFilled" @click="visible = true">提交</el-button>
            </div>
            <el-table :data="tableData" border class="table" ref="multipleTable" header-cell-class-name="table-header" @selection-change="handleSelectionChange" @sort-change="handleSortChange">
                <el-table-column type="selection" align="center"/>
                <el-table-column type="expand">
                    <template #default="scope">
                        <div class="snapshot-image">
                            <div v-for="fit in scope.row.snapshot" :key="fit" class="block">
                                <el-image :src="fit.url" fit="fill" />
                                <span class="snapstration">{{ fit.note }}</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="应用名" align="center" show-overflow-tooltip>
                    <template #default="scope"> <el-link :underline="true" type="primary" :href="scope.row.download_url"> {{ scope.row.name }}  </el-link>  </template>
                </el-table-column>
                
                <el-table-column prop="l_class" label="小类ID" width="80" align="center">
                    <template #default="scope">
                        <template v-if="has_permiss">
                            <el-link :underline="true" type="primary" @click="goToPlatform(scope.row.l_class)">{{ scope.row.l_class }}</el-link>
                        </template>
                        <template v-else>
                            {{ scope.row.l_class }}
                        </template>
                    </template>
                </el-table-column>
                <el-table-column prop="package" label="包名" align="center" show-overflow-tooltip></el-table-column>
                <el-table-column prop="priority" label="优先级" width="80" align="center"></el-table-column>
                <el-table-column prop="result" label="结果" width="100" align="center"></el-table-column>
                <el-table-column prop="remark" label="初步原因" align="center" show-overflow-tooltip></el-table-column>
                <!-- <el-table-column prop="reason" label="分析结论" align="center"></el-table-column> -->
                <el-table-column prop="updated_at" label="更新时间" width="160" align="center" sortable></el-table-column>
                <el-table-column prop="author" v-if="author_permiss" label="作者" width="80" align="center"></el-table-column>
                <el-table-column label="脚本类型" align="center" width="120">
                    <template #default="scope">
                        <el-tag v-if="scope.row.block_script == 1" type="warning" style="margin-right: 4px;">阻断</el-tag>
                        <el-tag v-if="scope.row.identify_script == 1" type="success">识别</el-tag>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="210" align="center">
                    <template #default="scope">
                        <el-button
                            type="primary"
                            link
                            :disabled="scope.row.delete == 1 || (scope.row.block_script == 0 && scope.row.identify_script == 1) ? true : false"
                            @click="handleEdit(scope.row)"
                        >
                            分析
                        </el-button>

						<el-dropdown
							@command="(cmd: string) => handleRunCommand(cmd, scope.row)"
							:disabled="scope.row.delete == 1"
							style="vertical-align: middle;"
							>
							<el-button type="success" link>
							执行
							<el-icon><arrow-down /></el-icon>
							</el-button>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item
									v-if="scope.row.block_script == 1"
									command="script"
									>阻断解析</el-dropdown-item>
									<el-dropdown-item
									v-if="scope.row.identify_script == 1"
									command="identify"
									:disabled="!has_permiss"
									>识别解析</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
                        <el-button
                            type="info"
                            link
                            :disabled="scope.row.delete == 1 ? true : false"
                            @click="handleRecord(scope.row)"
                        >
                            历史
                        </el-button>
                        <el-button
                            v-if="author_permiss"
                            type="danger"
                            link
                            @click="delVisible(scope.row)"
                        >
                        {{ scope.row.delete == 0 ? '删除' : '启用' }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    background
                    layout="total, prev, pager, next"
                    :current-page="query.pageIndex"
                    :page-size="query.pageSize"
                    :total="pageTotal"
                    @current-change="handlePageChange"
                ></el-pagination>
            </div>
            <el-button
                type="primary"
                size="small"
                @click="getScriptTableData(query)"
                >
                刷新表格
            </el-button>
            <el-button
                type="warning"
                size="small"
                @click="appTableDownload(query)"
                >
                下载表格
            </el-button>
            <el-dropdown
                @command="(cmd: string) => handleRefreshCommand(cmd)"
                style="vertical-align: middle;"
                >
                <el-button type="success" size="small">
                重新下发
                <el-icon><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="block">阻断下发</el-dropdown-item>
                        <el-dropdown-item command="identify">识别下发</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
            <el-button 
                type="primary" 
                size="small"
                @click="download_all" 
                :disabled="selectedValue.length === 0"
                >
                下载脚本
            </el-button>

            <el-dialog
                title="录制脚本提交"
                v-model="visible"
                width="500px"
                destroy-on-close
                :close-on-click-modal="false"
                @close="closeDialog"
                >
                <TableEdit 
                    :script_type="query.table_type" 
                    @change_visible="changeVisible" 
                    @refresh_table="handleSearch" 
                />
            </el-dialog>

            <el-dialog
                title="分析"
                v-model="visibleEdit"
                width="600px"
                destroy-on-close
                :close-on-click-modal="false"
                @close="closeEditDialog"
                >
                <TableEdit2 :data="rowData" :scriptType="query.table_type" @disableEdit="changeEditVisible" @refreshTb="getScriptTableData(query)"/>
            </el-dialog>

            <el-dialog
                v-model="delDialogVisible"
                title="警告"
                width="500"
                align-center
                >
                <span>确定要 {{ delRow.delete == 0 ? '删除' : '启用' }} {{ delRow.name }} 吗？</span>
                <template #footer>
                    <div class="dialog-footer">
                        <el-button type="danger" @click="handleDel(1)">
                        彻底删除
                        </el-button>
                        <el-button type="primary" @click="handleDel(0)">
                        {{ delRow.delete == 0 ? '保留副本' : '启用' }}
                        </el-button>
                        <el-button @click="delDialogVisible = false">取消</el-button>
                    </div>
                </template>
            </el-dialog>
            <el-dialog
                v-model="recordVisible"
                title="历史记录"
                width="1200px"
                align-center
                >
                <el-table :data="tableRecordData" border class="table" ref="multipleTable" header-cell-class-name="table-header">
                    <el-table-column prop="id" label="ID" width="60" align="center"></el-table-column>
                    <el-table-column prop="mobile_ip" label="手机IP" width="140" align="center"></el-table-column>
                    <el-table-column prop="router_host" label="路由IP" width="140" align="center"></el-table-column>
                    <el-table-column prop="start_time" label="开始时间" width="160" align="center"></el-table-column>
                    <el-table-column prop="end_time" label="结束时间" width="160" align="center"></el-table-column>
                    <el-table-column prop="cur_result" label="结果" width="60" align="center"></el-table-column>
                    <el-table-column prop="self_reason" label="分析结论" width="250" align="center"></el-table-column>
                    <el-table-column prop="measure" label="修改方式" width="200" align="center"></el-table-column>
                </el-table>
                <div class="pagination">
                <el-pagination
                        background
                        layout="total, prev, pager, next"
                        :current-page="queryRecord.pageIndex"
                        :page-size="queryRecord.pageSize"
                        :total="pageRecordTotal"
                        @current-change="recordPageChange"
                    ></el-pagination>
                </div>
                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="recordVisible = false">关闭</el-button>
                    </div>
                </template>
            </el-dialog>
            <el-dialog
                title="表格下载"
                v-model="visibleApp"
                width="200px"
                destroy-on-close
                :close-on-click-modal="false"
                >
                <el-text v-if="visibleTip">{{ tipMsg }}</el-text>
                <el-link v-if="visibleLink" :underline="true" type="primary" :href="fileLink">点击下载</el-link>
                <el-progress :percentage="appProgress" />
                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="visibleApp = false">关闭</el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
    </div>
</template>

<script setup lang="ts" name="script_table">
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, CirclePlusFilled, ArrowDown } from '@element-plus/icons-vue';
import { ScriptTable, getAuthors, exeScript, delScript, refreshInspection, getRecordScript, apiAppTable, downloadAllFiles } from '../api/index';
import TableEdit from '../components/table-edit.vue';
import TableEdit2 from '../components/table-detail.vue';
import { usePermissStore } from '../store/permiss';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const permiss = usePermissStore();
const author_permiss = permiss.key.indexOf('4') == -1 ? false : true; 

const has_permiss = permiss.key.indexOf('5') == -1 ? false : true;

interface ListItem {
  value: number,
  label: string
}

let authors_options = ref<ListItem[]>([]);

interface TableItem {
    id: number;
    name: string;
    l_class: string;
    package: number;
    priority: string;
    result: string;
    remark: string;
    delete: number;
    author: number;
    created_at: Date;
    updated_at: Date;
    mobile_ip: string;
    router_host: string;
    router_port: number;
    extend_device: string;
    self_reason: string;
    cur_result: number;
    measure: string;
    start_time: string;
    end_time: string;
    extract: string;
    original: string;
    designate: string;
    record_id: number;
    table_type: string;
    block_script: number;
    identify_script: number;
}

const query = reactive({
    time: [],
    delete: 0,
    name: "",
    l_class: '',
    package: '',
    priority: '',
    result: '',
    author: '',
    script_type: '',
    taskId: '',
    pageIndex: 1,
    pageSize: 10,
    table_type: 'cn_android',
    sortBy: undefined,
    sortOrder: undefined
});
const tableData = ref<TableItem[]>([]);
const pageTotal = ref(0);

const delete_options = [
    {
        value: 0,
        label: '已启用',
    },
    {
        value: 1,
        label: '已删除',
    }
]

const status_options = [
    {
        value: 1,
        label: '低',
    },
    {
        value: 2,
        label: '中',
    },
    {
        value: 3,
        label: '高',
    }
]

const result_options = [
    {
        value: '成功',
        label: '成功',
    },
    {
        value: '失败',
        label: '失败',
    },
    {
        value: '未出结果',
        label: '未出结果',
    }
]

const scriptTypeOptions = [
  { label: '阻断', value: 'block' },
  { label: '识别', value: 'identify' }
];

const platformOptions = [
  { label: '国内安卓', value: 'cn_android' },
  { label: '国外安卓', value: 'en_android' },
  { label: '国内IOS', value: 'cn_ios' },
  { label: '国外IOS', value: 'en_ios' }
];

const handleSortChange = ({ prop, order }) => {
  query.sortBy = order ? prop : undefined;
  query.sortOrder = order || undefined;
  getScriptTableData(query);
};

const getScriptTableData = async (tableInfo: any) => {
  try {
    const res = await ScriptTable(tableInfo);
    tableData.value = res.data.list;
    pageTotal.value = res.data.total;
  } catch (err) {
    ElMessage.error('获取国内安卓脚本数据异常！');
  }
}

const remoteMethod = async () => {
    try {
        const res = await getAuthors();
        authors_options.value = res.data;
    } catch (err) {
        ElMessage.error('查询作者异常！');
    }
};
onMounted(() => {
    remoteMethod();
    getScriptTableData(query);
});
// 查询操作
const handleSearch = () => {
    query.pageIndex = 1;
    getScriptTableData(query);
};
// 分页导航
const handlePageChange = (val: number) => {
    query.pageIndex = val;
    getScriptTableData(query);
};


const visible = ref(false);


const closeDialog = () => {
    visible.value = false;
};

const changeVisible = () => {
    visible.value = false;
};

// 编辑操作
const visibleEdit = ref(false);
const rowData = ref({});
const delDialogVisible = ref(false);

const closeEditDialog = () => {
    visibleEdit.value = false;
};

const handleEdit = (row: TableItem) => {
    rowData.value = row;
    visibleEdit.value = true;
};

const runScript = async (row: TableItem, scriptTypeOverride?: string) => {
    try {
        const rsp = await exeScript(scriptTypeOverride, row)
        if (rsp.data.code == 200) {
            ElMessage({
                showClose: true,
                message: rsp.data.message,
                type: "success",
                duration: 10000
            });
            getScriptTableData(query);
        } else {
            ElMessage.error(rsp.data.message);
        }
    } catch (err) {
        ElMessage.error('执行异常！');
    }
}

const delRow = ref({
    id: -1,
    name: "",
    delete: 0
});

const delRowScript = async (del_store: number) => {
    try {
        const rsp = await delScript(query.table_type, delRow.value.id, del_store);
        if (rsp.data.code == 200) {
            ElMessage.success(rsp.data.message);
            getScriptTableData(query);
        } else {
            ElMessage.error(rsp.data.message)
        }
    } catch (err) {
        ElMessage.error("删除数据异常！");
    }
}


const delVisible = (row: TableItem) => {
    delDialogVisible.value = true;
    delRow.value.id = row.id;
    delRow.value.name = row.name;
    delRow.value.delete = row.delete;
};

const handleDel = (del_store: number) => {
    delDialogVisible.value = false;
    delRowScript(del_store);
}

const changeEditVisible = () => {
    visibleEdit.value = false;
};


const refresh = async (mode?: string) => {
    try {
        const rsp = await refreshInspection(query.table_type, mode);
        if (rsp.data.code == 200) {
            ElMessage.success(rsp.data.message);
        } else {
            ElMessage.error(rsp.data.message);
        }
    } catch (err) {
        ElMessage.error("服务器异常！")
    }
}

const handleRefreshCommand = (command: string) => {
    refresh(command);
};

// 历史记录
const recordVisible = ref(false);
interface TableRecord {
    id: number;
    mobile_ip: string;
    router_host: string;
    self_reason: string;
    cur_result: number;
    measure: string;
    start_time: string;
    end_time: string;
    task_name: string;
}

const queryRecord = reactive({
    id: 0,
    pageIndex: 1,
    pageSize: 10
});
const tableRecordData = ref<TableRecord[]>([]);
const pageRecordTotal = ref(0);

const getRecordData = async (tableInfo: any) => {
    try {
        const res = await getRecordScript(query.table_type, tableInfo);
        recordVisible.value = true;
        tableRecordData.value = res.data.list;
        pageRecordTotal.value = res.data.total;
    } catch (err) {
        ElMessage.error('获取历史记录数据异常！');
    }
}

const recordScript = async (row: TableItem) => {
    queryRecord.id = row.id;
    queryRecord.pageIndex = 1;
    getRecordData(queryRecord);
}

const recordPageChange = (val: number) => {
    queryRecord.pageIndex = val;
    getRecordData(queryRecord);
};

const handleRecord = (row: TableItem) => {
    recordScript(row);
};

const visibleApp = ref(false);
const tipMsg = ref("");
const appProgress = ref(0);
const visibleTip = ref(false);
const visibleLink = ref(false);
const fileLink = ref("#");
const appTableDownload = async (tableInfo: any) => {
    visibleApp.value = true;
    visibleTip.value = true;
    visibleLink.value = false;
    fileLink.value = "#";
    tipMsg.value = "表格生成中...";
    appProgress.value = Math.floor(Math.random() * 100);
    try {
        const rsp = await apiAppTable(query.table_type, tableInfo);
        if (rsp.data.code == 200) {
            visibleTip.value = false;
            appProgress.value = 100;
            visibleLink.value = true;
            fileLink.value = rsp.data.link;
        } else {
            tipMsg.value = "表格生成失败，请重试！"
        }
    } catch (err) {
        ElMessage.error('表格下载异常！');
    }
}

const selectedValue = ref([]);
const handleSelectionChange = (selection: any[]) => {
    selectedValue.value = selection.map(item => item.download_url);
};

const download_all = async () => {
    downloadAllFiles(Object.values(selectedValue.value));
};
const router = useRouter();

const goToPlatform = (l_class: string) => {
  let domestic = 0;
  if (query.table_type === 'en_android' || query.table_type === 'en_ios') {
    domestic = 1;
  }
  router.push({
    path: `/application_table`,
    query: { l_class: l_class, domestic }
  });
}

const handleRunCommand = (command: string, row: TableItem) => {
  let scriptType = '';
  if (query.table_type === 'cn_android') {
    scriptType = command === 'script' ? 'cn_android' : 'cn_android_identify';
  } else if (query.table_type === 'en_android') {
    scriptType = command === 'script' ? 'en_android' : 'en_android_identify';
  } else if (query.table_type === 'cn_ios') {
    scriptType = command === 'script' ? 'cn_ios' : 'cn_ios_identify';
  } else if (query.table_type === 'en_ios') {
    scriptType = command === 'script' ? 'en_ios' : 'en_ios_identify';
  }
  runScript(row, scriptType);
};

watch(
  [() => route.query.table_type, () => route.query.l_class],
  async ([tableType, lClass]) => {
    if (tableType && lClass) {
      query.delete = 0;
      query.name = '';
      query.package = '';
      query.priority = '';
      query.result = '';
      query.author = '';
      query.script_type = '';
      query.pageIndex = 1;
      query.table_type = tableType as string;
      query.l_class = lClass as string;
      await getScriptTableData(query);
      router.replace({ query: {} });
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.search-box {
    margin-bottom: 10px;
}

.search-input {
    width: 120px;
}

.mr10 {
    margin-right: 10px;
}

.mrl10 {
    margin-left: 10px;
}

.table-td-thumb {
    display: block;
    margin: auto;
    width: 40px;
    height: 40px;
}

.snapshot-image .block {
  padding: 10px 0;
  text-align: center;
  border-right: solid 1px var(--el-border-color);
  display: inline-block;
  width: 20%;
  box-sizing: border-box;
  vertical-align: top;
}

.snapshot-image .block:last-child {
  border-right: none;
}

.snapshot-image .snapstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}
/* 添加到现有的样式中 */
:deep(.highlight-row) {
    background-color: #fdf5e6 !important;  /* 浅橙色背景 */
    transition: background-color 0.3s;      /* 添加过渡效果 */
}

:deep(.highlight-row:hover) {
    background-color: #ffe4b5 !important;  /* 鼠标悬停时的颜色 */
}

/* 可以添加一个动画效果 */
@keyframes highlight-flash {
    0% { background-color: #fff; }
    50% { background-color: #fdf5e6; }
    100% { background-color: #fdf5e6; }
}

:deep(.highlight-row) {
    animation: highlight-flash 1s ease-in-out;
}
</style>
