<template>
	<div>
		<div class="container">
			<div class="search-box">
				<el-row :gutter="10">
					<el-col :span="4">
						<el-input v-model="query.name" placeholder="项目名称" clearable/>
					</el-col>
					<el-col :span="4">
						<el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
					</el-col>
				</el-row>
			</div>

			<el-table :data="tableData" border class="table" ref="multipleTable">
				<el-table-column prop="name" label="项目名称" align="center"/>
				<el-table-column prop="district" label="区域" align="center" />
				<el-table-column prop="version" label="版本号" align="center"/>
				<el-table-column prop="discorb" label="配置描述" align="center"/>
				<el-table-column prop="notes" label="备注" align="center"/>
				<el-table-column prop="version_time" label="发布时间" align="center"/>
				<el-table-column label="下载" width="220" align="center">
					<template #default="scope">
						<el-button
							type="primary"
							@click="handleDownload(scope.row.publish_url, '发布版本')"
							size="small"
						>发布版本</el-button>
						<el-button
							type="success"
							@click="handleDownload(scope.row.db_url, '数据库')"
							size="small"
							v-if="scope.row.db_url"
						>数据库</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div class="pagination">
				<el-pagination
					background
					layout="total, prev, pager, next"
					:current-page="query.pageIndex"
					:page-size="query.pageSize"
					:total="pageTotal"
					@current-change="handlePageChange"
				></el-pagination>
			</div>

		</div>
	</div>
</template>

<script setup lang="ts" name="version_table_basetable">
import { ref, reactive, onActivated } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { versioningQuery } from '../api/index';

// 查询对象
const query = reactive({
	name: '',
	pageIndex: 1,
	pageSize: 10
});

// 表格数据
const tableData = ref([]);
const pageTotal = ref(0);

// 获取版本数据
const getVersionData = async () => {
	try {
		const res = await versioningQuery(query);
		tableData.value = res.data.list;
		pageTotal.value = res.data.total;
	} catch (err) {
		ElMessage.error('获取版本数据异常！');
	}
};

onActivated(() => {
	getVersionData();
});

// 搜索
const handleSearch = () => {
	query.pageIndex = 1;
	getVersionData();
};

// 分页
const handlePageChange = (val: number) => {
	query.pageIndex = val;
	getVersionData();
};

const getFileNameFromContentDisposition = (contentDisposition: string, fallback: string) => {
	if (!contentDisposition) return fallback;
	// 优先匹配 filename*=
	const filenameStarMatch = contentDisposition.match(/filename\*\=([^;]+)/i);
	if (filenameStarMatch) {
		// 形如 UTF-8''%E9%94%90%E6%8D%B7_cn-1.0.0.db
		let value = filenameStarMatch[1].trim();
		// 处理编码前缀
		const parts = value.split("''");
		if (parts.length === 2) {
			try {
				return decodeURIComponent(parts[1]);
			} catch {
				return parts[1];
			}
		}
		return value;
	}
	// 再匹配 filename=
	const filenameMatch = contentDisposition.match(/filename=([^;]+)/i);
	if (filenameMatch) {
		return filenameMatch[1].replace(/(^\"|\"$)/g, '').trim();
	}
	return fallback;
};

const handleDownload = async (url: string, label: string) => {
	if (!url) {
		ElMessage.error(`${label}下载链接不存在`);
		return;
	}
	try {
		const response = await fetch(url);
		if (!response.ok) {
			const errorData = await response.json();
			ElMessage.error(errorData.message || `${label}下载失败`);
			return;
		}
		const blob = await response.blob();
		const downloadUrl = window.URL.createObjectURL(blob);
		const link = document.createElement('a');
		link.href = downloadUrl;
		link.style.display = 'none';

		const contentDisposition = response.headers.get('Content-Disposition');
		const fileName = getFileNameFromContentDisposition(contentDisposition, `${label}下载文件`);
		link.download = fileName;

		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		window.URL.revokeObjectURL(downloadUrl);
		ElMessage.success(`${label}下载成功`);
	} catch (error) {
		console.error('下载出错:', error);
		ElMessage.error(`${label}下载过程中出现错误，请稍后重试`);
	}
};

</script>

<style scoped>
.container {
	padding: 20px;
}
.search-box {
	margin-bottom: 20px;
}
.pagination {
	margin-top: 20px;
	text-align: right;
}
.dialog-footer {
	text-align: center;
}
</style>
