<template>
	<div>
		<div class="container">
			<div class="search-box">
				<el-select
					v-model="query.domestic"
					placeholder="状态"
					class="search-input mr10"
					@change="handleDomesticChange"
					>
					<el-option
						v-for="item in domestic_options"
							:key="item.value"
							:label="item.label"
							:value="item.value"
					/>
    			</el-select>
				<el-select
					v-model="query.items"
					placeholder="项目"
					class="search-input mr10"
					>
					<el-option
						v-for="item in itemsOptions"
							:key="item.value"
							:label="item.label"
							:value="item.value"
					/>
    			</el-select>
				<el-button type="primary" :icon="DocumentAdd" @click="handleAddVersion">新增</el-button>
				<el-button type="info" :icon="Search" @click="handleSearch">搜索</el-button>
			</div>
			
			<el-table 
				:data="tableData" 
				border 
				class="table" 
				ref="multipleTable" 
				header-cell-class-name="table-header"
			>
				
				<el-table-column prop="id" label="id" width="80" align="center"></el-table-column>

				<el-table-column prop="ctitle" label="ctitle" align="center"></el-table-column>
                <el-table-column prop="cdata" label="cdata" align="center"></el-table-column>
                <el-table-column prop="ctype" label="ctype" width="80" align="center"></el-table-column>
                <el-table-column prop="cactive" label="cactive" width="100" align="center">
					<template #default="{ row }">
						<el-tag
						:type="row.cactive === '1' ? 'success' : 'danger'"
						size="small"
						>
						{{ row.cactive === '1' ? '开启' : '关闭' }}
						</el-tag>
					</template>
				</el-table-column>
                <el-table-column prop="cdesc" label="cdesc" align="center"></el-table-column>
				
				<el-table-column fixed="right" label="操作" width="160" align="center">
					<template #default="scope">
						<el-button
							type="primary"
							@click="handleEditConfig(scope.row)"
						>
                            编辑
						</el-button>
						<el-button
							type="danger"
							@click="handleDelConfig(scope.row)"
						>
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div class="pagination">
				<el-pagination
					background
					layout="total, prev, pager, next"
					:current-page="query.pageIndex"
					:page-size="query.pageSize"
					:total="pageTotal"
					@current-change="handlePageChange"
				></el-pagination>
			</div>


			<el-dialog
				v-model="configDelVisible"
				title="警告"
				width="500"
				align-center
				>
				<span>确定要删除配置 {{ configDelForm.ctitle }} 吗？</span>
				<template #footer>
					<div class="dialog-footer">
						<el-button type="danger" :icon="Delete" @click="handleConfigDel">删除</el-button>
						<el-button :icon="CloseBold" @click="configDelVisible = false">取消</el-button>
					</div>
				</template>
			</el-dialog>

			<el-dialog 
				v-model="configFormVisible" 
				:title="formType === 'add' ? '新增配置' : '编辑配置'" 
				style="max-width: 500px;"
				>
				<el-form 
					ref="configFormRef"
					:model="configForm"
					label-width="auto"
					>
					<el-form-item label="区域" v-if="formType === 'add'">
						<el-select
							v-model="configForm.domestic"
							placeholder="请选择区域"
							@change="handleAddDomesticChange"
							style="width: 100%;"
							>
							<el-option
								v-for="item in domestic_options"
									:key="item.value"
									:label="item.label"
									:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="项目" v-if="formType === 'add'">
						<el-select
							v-model="configForm.items"
							placeholder="请选择项目"
							style="width: 100%;"
							>
							<el-option
								v-for="item in itemsAddOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="标题">
						<el-input 
							v-model="configForm.ctitle" 
							placeholder="请输入标题"
							:class="{ 'modified-input': isFieldModified('ctitle') }"
						/>
					</el-form-item>
					<el-form-item label="数据">
						<el-input 
							v-model="configForm.cdata" 
							placeholder="请输入数据"
							:class="{ 'modified-input': isFieldModified('cdata') }"
						/>
					</el-form-item>
					<el-form-item label="类型">
						<el-input 
							v-model="configForm.ctype" 
							placeholder="请输入类型"
							:class="{ 'modified-input': isFieldModified('ctype') }"
						/>
					</el-form-item>
                    <el-form-item label="状态">
					<el-switch
						v-model="configForm.cactive"
						active-value="1"
						inactive-value="0"
						:active-text="configForm.cactive === '1' ? '开启' : '关闭'"
						:class="{ 'modified-input': isFieldModified('cactive') }"
					/>
					</el-form-item>
                    <el-form-item label="描述">
						<el-input 
							v-model="configForm.cdesc" 
							type="textarea" 
							placeholder="请输入描述"
							:class="{ 'modified-input': isFieldModified('cdesc') }"
						/>
					</el-form-item>
				</el-form>
				<template #footer>
					<el-button 
						type="primary" 
						:icon="formType === 'add' ? Promotion : EditPen" 
						@click="handlePreview"
						:loading="submitLoading"
					>{{ formType === 'add' ? '提交' : '保存' }}</el-button>
					<el-button :icon="CloseBold" @click="configFormVisible = false">取消</el-button>
				</template>
			</el-dialog>

			<!-- 预览确认弹窗 -->
			<el-dialog 
				v-model="previewVisible"
				title="确认配置"
				width="50%"
				>
				<div class="preview-content">
					<pre><code v-html="formatPreviewContent()"></code></pre>
				</div>
				<template #footer>
					<el-button type="primary" @click="handleConfigSubmit">确认</el-button>
					<el-button @click="previewVisible = false">取消</el-button>
				</template>
			</el-dialog>
			
		</div>
	</div>
</template>

<script setup lang="ts" name="config_table_basetable">
import { ref, reactive } from 'vue';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Search, DocumentAdd, Promotion, CloseBold, EditPen, Delete } from '@element-plus/icons-vue';
import { snfConfig, snfConfigAdd, snfConfigEdit, snfConfigDel, snfVersionItems } from '../api/index';


interface TableItem {
	id: number;
	domestic: number;
	items: number;
	ctitle: string;
	cdata: string;
	ctype: string;
    cactive: string;
    cdesc: string;
}

const query = reactive({
	domestic: 0,
	items: 1,
	pageIndex: 1,
	pageSize: 10
});
const tableData = ref<TableItem[]>([]);
const pageTotal = ref(0);

const domestic_options = [
	{
    	value: 0,
    	label: '国内',
  	},
	{
		value: 1,
		label: '国外',
	}
]

const projects = ref([]);
const itemsOptions = ref([]);


const getSnfConfigData = async (tableInfo: any) => {
	try {
		const res = await snfConfig(tableInfo);
		if (res.data.code == 200) {
			tableData.value = res.data.list.map((item: any) => ({
				...item,
				isModified: false
			}));
			pageTotal.value = res.data.total;
		} else {
			ElMessage.error(res.data.message)
		}
		
	} catch (err) {
		ElMessage.error('获取配置数据异常！');
	}
}

const setItemsOptions = () => {
	for (let i=0; i<projects.value.length; i++) {
		if (query.domestic == projects.value[i].domestic) {
			itemsOptions.value = projects.value[i].list;
			query.items = itemsOptions.value[0].value;
			break;
		}
	}
}

const getSnfVersionItems = async () => {
	try {
		const res = await snfVersionItems();
		if (res.data.code == 200) {
			projects.value = res.data.list;	
			setItemsOptions();
		}	
	} catch (err) {
		ElMessage.error('获取项目数据异常！');
	}	
}

getSnfVersionItems();

getSnfConfigData(query);


const handleDomesticChange = () => {
	setItemsOptions();
}


// 查询操作
const handleSearch = () => {
	query.pageIndex = 1;
	getSnfConfigData(query);
};
// 分页导航
const handlePageChange = (val: number) => {
	query.pageIndex = val;
	getSnfConfigData(query);
};


// 表单相关
const configFormRef = ref<FormInstance>();
const configFormVisible = ref(false);
const formType = ref<'add' | 'edit'>('add');
const submitLoading = ref(false);
const itemsAddOptions = ref([]);

const configForm = reactive({
	id: 0,
	domestic: 0,
	items: 1,
	ctitle: '',
	cdata: '',
	ctype: '',
    cactive: '',
    cdesc: ''
});

// 保存原始数据
const originalData = ref<TableItem>({
	id: 0,
	domestic: 0,
	items: 1,
	ctitle: '',
	cdata: '',
	ctype: '',
    cactive: '',
    cdesc: ''
});

// 重置表单
const resetForm = () => {
	if (configFormRef.value) {
		configFormRef.value.resetFields();
	}
	configForm.id = 0;
	configForm.domestic = 0;
	configForm.items = 1;
	configForm.ctitle = '';
	configForm.cdata = '';
	configForm.ctype = '';
	configForm.cactive = '';
	configForm.cdesc = '';
};

// 新增配置
const handleAddVersion = () => {
	formType.value = 'add';
	resetForm();
	configFormVisible.value = true;
	handleAddDomesticChange();
};

// 编辑配置
const handleEditConfig = (row: TableItem) => {
	formType.value = 'edit';
	configFormVisible.value = true;
	// 保存原始数据
	originalData.value = { ...row };
	Object.assign(configForm, row);
};

// 检查字段是否被修改
const isFieldModified = (field: string) => {
    if (formType.value === 'add') {
        return configForm[field] !== ''; // 新增时，只要有值就高亮
    }
    return configForm[field] !== originalData.value[field]; // 编辑时，与原值不同则高亮
};

// 预览确认弹窗
const previewVisible = ref(false);
const previewContent = ref('');

// 生成预览内容
const generatePreviewContent = () => {

	console.log(configForm);
    const domesticObj = domestic_options.find(opt => opt.value === configForm.domestic);
    const domestic = domesticObj ? domesticObj.label : configForm.domestic;

    const oldCfgItem = `<cfg_item ctitle="${originalData.value.ctitle}" cdata="${originalData.value.cdata}" ctype="${originalData.value.ctype}" cactive="${originalData.value.cactive}" cdesc="${originalData.value.cdesc}"/>`;
    const newCfgItem = `<cfg_item ctitle="${configForm.ctitle}" cdata="${configForm.cdata}" ctype="${configForm.ctype}" cactive="${configForm.cactive}" cdesc="${configForm.cdesc}"/>`;

    // 获取项目 label 和 value
    const itemsArr = formType.value === 'add' ? itemsAddOptions.value : itemsOptions.value;
    // items映射表
    const itemsMap = {
        1: 'ruijie',
        2: 'h3c',
        3: 'ruijie',
        '锐捷_cn': 'ruijie',
        '锐捷_en': 'ruijie',
        '新华三': 'h3c',
    };

    const itemsObj = itemsArr.find(opt => String(opt.value) === String(configForm.items));
    const itemsLabel = itemsObj ? itemsObj.label : configForm.items;
    const itemsKey = itemsObj ? itemsObj.value : configForm.items;
    const itemsValue = itemsMap[itemsKey] || itemsKey;

    let lines = [];
    lines.push(`${domestic} | ${itemsLabel}`);
   
    if (formType.value === 'edit') {
		lines.push(`<sub_common items="${itemsValue}">`);
        if (oldCfgItem !== newCfgItem) {
            lines.push(`-${oldCfgItem}`);
            lines.push(`+${newCfgItem}`);
        } else {
            lines.push(`${oldCfgItem}`);
        }
		lines.push(`</sub_common>`);
    } else {
        // 新增模式，所有行都加 +
        lines.push(`+<sub_common items="${itemsValue}">`);
        lines.push(`+${newCfgItem}`);
        lines.push(`+</sub_common>`);
    }
    

    // 转义HTML
    return lines.map(line =>
        line.replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;')
    ).join('\n');
};

// 显示预览
const handlePreview = () => {
    previewContent.value = generatePreviewContent();
    previewVisible.value = true;
};

// 提交表单
const handleConfigSubmit = async () => {
	submitLoading.value = true;
	try {
		const rsp = await (formType.value === 'add' ? snfConfigAdd(configForm) : snfConfigEdit(configForm));
		if (rsp.data.code == 200) {
			ElMessage.success(rsp.data.message);
			query.pageIndex = 1;
			previewVisible.value = false;
			configFormVisible.value = false;
			getSnfConfigData(query);
		} else {
			ElMessage.error(rsp.data.message);
		}
	} catch (err) {
		ElMessage.error(formType.value === 'add' ? "新增配置数据异常！" : "编辑配置数据异常！");
	} finally {
		submitLoading.value = false;
}
};

// 删除版本配置
const configDelVisible = ref(false);
const configDelForm = reactive({
	id: 0,
	ctitle: ""
});

const handleDelConfig = (row: TableItem) => {
	configDelVisible.value = true;
	configDelForm.id = row.id;
	configDelForm.ctitle = row.ctitle;
}


const configDelSubmit = async () => {
	try {
		const rsp = await snfConfigDel(configDelForm);
		if (rsp.data.code == 200) {
			ElMessage.success(rsp.data.message);
            query.pageIndex = 1;
            configDelVisible.value = false;
			getSnfConfigData(query);
		} else {
			ElMessage.error(rsp.data.message);
		}
	} catch (err) {
		ElMessage.error("删除配置数据异常！");
	}
}

const handleConfigDel = () => {
	configDelSubmit();
}

// 处理区域变化
const handleAddDomesticChange = () => {
	for (let i=0; i<projects.value.length; i++) {
		if (configForm.domestic == projects.value[i].domestic) {
			itemsAddOptions.value = projects.value[i].list;
			configForm.items = itemsAddOptions.value[0].value;
			break;
		}
	}
};

// 格式化预览内容
const formatPreviewContent = () => {
    const lines = previewContent.value.split('\n');
    return lines.map(line => {
        if (line.startsWith('-')) {
            return `<div class="line-deleted">${line.substring(1)}</div>`;
        } else if (line.startsWith('+')) {
            return `<div class="line-added">${line.substring(1)}</div>`;
        }
        return `<div class="line-normal">${line}</div>`;
    }).join('');
};

</script>

<style scoped>
.search-box {
	margin-bottom: 10px;
}

.search-input {
	width: 120px;
}

.mr10 {
	margin-right: 10px;
}

.mrl10 {
	margin-left: 10px;
}

.table-td-thumb {
	display: block;
	margin: auto;
	width: 40px;
	height: 40px;
}

.snapshot-image .block {
  padding: 10px 0;
  text-align: center;
  border-right: solid 1px var(--el-border-color);
  display: inline-block;
  width: 20%;
  box-sizing: border-box;
  vertical-align: top;
}

.snapshot-image .block:last-child {
  border-right: none;
}

.snapshot-image .snapstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.modified-field {
	background-color: #ecf5ff;
}

.modified-field :deep(.el-input__wrapper) {
	background-color: #ecf5ff;
}

.modified-field :deep(.el-textarea__inner) {
	background-color: #ecf5ff;
}

.modified-input :deep(.el-input__wrapper) {
	background-color: #fff3e6 !important;
}

.modified-input :deep(.el-textarea__inner) {
	background-color: #fff3e6 !important;
}

.preview-content {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    font-family: monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.preview-content pre {
    margin: 0;
}

.preview-content pre code {
    display: block;
    line-height: 1.5;
}

/* 添加和删除的行样式 */
.preview-content pre code ::v-deep(span) {
    display: block;
    width: 100%;
}

.preview-content pre code ::v-deep(span[data-prefix="-"]) {
    background-color: #ffdce0;
    color: #67060c;
}

.preview-content pre code ::v-deep(span[data-prefix="+"]) {
    background-color: #cdffd8;
    color: #0a3622;
}

.preview-content :deep(.line-normal) {
    padding: 4px 8px;
}

.preview-content :deep(.line-deleted) {
    background-color: #ffdce0;
    color: #67060c;
    padding: 4px 8px;
    position: relative;
}

.preview-content :deep(.line-deleted)::before {
    content: "-";
    position: absolute;
    left: 0;
    width: 32px;
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    color: #d32f2f;
}

.preview-content :deep(.line-added) {
    background-color: #cdffd8;
    color: #0a3622;
    padding: 4px 8px;
    position: relative;
}

.preview-content :deep(.line-added)::before {
    content: "+";
    position: absolute;
    left: 0;
    width: 32px;
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    color: #388e3c;
}

/* 添加和删除的行样式增强分隔感 */
.preview-content :deep(.line-added),
.preview-content :deep(.line-deleted) {
    margin-bottom: 10px;
    margin-top: 10px;
    padding-left: 36px;
}
</style>
