# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   inspection.py
@Time    :   2024/4/11 13:18
"""
import os
import datetime
from flask import jsonify, url_for, send_file
from flask_restful import Resource, reqparse
from werkzeug.datastructures import FileStorage
from sqlalchemy import and_
from app import db
from app.models import CnAndroid, EnAndroid, CnIOS, EnIOS, Task, TaskRecord, User
from app.resources.user import authenticate
from app.utils import work_update, task_template, read_task_file, write_task_detail_table
from config import Config


class Inspection(Resource):
    method_decorators = [authenticate]

    def get(self):
        pass
        # task = Task.query.order_by(Task.created_at.desc()).first()
        # if task:
        #     if task.status == 1:
        #         return {"code": 200, "over": 1, "message": "当前任务已结束"}
        #     return {"code": 200, "over": 0, "message": "当前任务未结束"}
        # else:
        #     return {"code": 200, "over": 1, "message": "未查询到数据"}

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, location="form")
        parser.add_argument("platform", type=str, location="form")
        parser.add_argument("mode", type=str, location="form")
        parser.add_argument('file', type=FileStorage, location='files')
        args = parser.parse_args()
        name = args.get("name")
        platforms = args.get("platform", "")
        _file = args.get('file')
        if not name or (not platforms and not _file):
            return {"code": 400, "message": "参数错误"}
        _tmp = Task.query.filter_by(name=name).first()
        if _tmp:
            return {"code": 400, "message": "任务名称重复！"}
        _task = Task(name=name)
        db.session.add(_task)
        db.session.commit()
        if platforms:
            pf = platforms.split("+")
            for item in pf:
                if item == Config.CN_ANDROID:
                    self.inspect_cn_android(args)
                if item == Config.EN_ANDROID:
                    self.inspect_en_android(args)
                if item == Config.CN_IOS:
                    self.inspect_cn_ios(args)
                if item == Config.EN_IOS:
                    self.inspect_en_ios(args)
        if _file:
            _f = self._file_task(_file, name)
            if not _f:
                return {"code": 400, "message": "任务文件异常"}
        return {"code": 200, "message": f"新增日常巡检成功！"}

    def _file_task(self, _file: FileStorage, task_name: str):
        try:
            filename = f"{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}-" + _file.filename
            path = os.path.join(Config.BASEDIR, 'tmp', filename)
            _file.save(path)
        except Exception as err:
            return False
        try:
            data = read_task_file(path)
            if (not data[Config.CN_ANDROID] and not data[Config.EN_ANDROID] and
                    not data[Config.CN_IOS] and not data[Config.EN_IOS]):
                return True
            task = Task.query.filter_by(name=task_name).first()
            cn_android_total, en_android_total, cn_ios_total, en_ios_total = task.cn_android_total, task.en_android_total, task.cn_ios_total, task.en_ios_total
            for item in data[Config.CN_ANDROID]:
                ca = CnAndroid.query.filter_by(l_class=item).first()
                if ca:
                    cn_android_total += 1
                    tr = TaskRecord(appid=ca.id, platform=1)
                    task.task_record.append(tr)
            task.cn_android_total = cn_android_total
            for item in data[Config.EN_ANDROID]:
                ea = EnAndroid.query.filter_by(l_class=item).first()
                if ea:
                    en_android_total += 1
                    tr = TaskRecord(appid=ea.id, platform=2)
                    task.task_record.append(tr)
            task.en_android_total = en_android_total
            for item in data[Config.CN_IOS]:
                ci = CnIOS.query.filter_by(l_class=item).first()
                if ci:
                    cn_ios_total += 1
                    tr = TaskRecord(appid=ci.id, platform=3)
                    task.task_record.append(tr)
            task.cn_ios_total = cn_ios_total
            for item in data[Config.EN_IOS]:
                ei = EnIOS.query.filter_by(l_class=item).first()
                if ei:
                    en_ios_total += 1
                    tr = TaskRecord(appid=ei.id, platform=4)
                    task.task_record.append(tr)
            task.en_ios_total = en_ios_total
            db.session.add(task)
            db.session.commit()
            return True
        except Exception as err:
            return False

    def inspect_cn_android(self, args: dict):
        data = list()
        name = args.get("name")
        cas = CnAndroid.query.filter(and_(CnAndroid.result == "成功", CnAndroid.delete == 0)).all()
        task = Task.query.filter_by(name=name).first()
        task.cn_android_total = len(cas)
        for item in cas:
            tr = TaskRecord(appid=item.id, platform=1)
            task.task_record.append(tr)
        data.append(task)
        db.session.add_all(data)
        db.session.commit()
        return {"code": 200, "message": f"新增日常巡检成功！"}

    def inspect_en_android(self, args: dict):
        data = list()
        name = args.get("name")
        cas = EnAndroid.query.filter(and_(EnAndroid.result == "成功", EnAndroid.delete == 0)).all()
        task = Task.query.filter_by(name=name).first()
        task.en_android_total = len(cas)
        for item in cas:
            tr = TaskRecord(appid=item.id, platform=2)
            task.task_record.append(tr)
        data.append(task)
        db.session.add_all(data)
        db.session.commit()
        return {"code": 200, "message": f"新增日常巡检成功！"}

    def inspect_cn_ios(self, args: dict):
        data = list()
        name = args.get("name")
        cas = CnIOS.query.filter(and_(CnIOS.result == "成功", CnIOS.delete == 0)).all()
        task = Task.query.filter_by(name=name).first()
        task.cn_ios_total = len(cas)
        for item in cas:
            tr = TaskRecord(appid=item.id, platform=3)
            task.task_record.append(tr)
        data.append(task)
        db.session.add_all(data)
        db.session.commit()
        return {"code": 200, "message": f"新增日常巡检成功！"}

    def inspect_en_ios(self, args: dict):
        data = list()
        name = args.get("name")
        cas = EnIOS.query.filter(and_(EnIOS.result == "成功", EnIOS.delete == 0)).all()
        task = Task.query.filter_by(name=name).first()
        task.en_ios_total = len(cas)
        for item in cas:
            tr = TaskRecord(appid=item.id, platform=4)
            task.task_record.append(tr)
        data.append(task)
        db.session.add_all(data)
        db.session.commit()
        return {"code": 200, "message": f"新增日常巡检成功！"}


class Inspection2(Resource):
    method_decorators = [authenticate]

    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument("task_id", type=int, location='args')
        args = parser.parse_args()
        task_id = args.get("task_id")
        if not task_id:
            return {"code": 400, "message": "参数错误"}
        task = Task.query.filter_by(id=task_id).first()
        if task:
            success = TaskRecord.query.filter(and_(TaskRecord.result == 1, TaskRecord.task_id == task.id)).count()
            failed = TaskRecord.query.filter(and_(TaskRecord.result == 2, TaskRecord.task_id == task.id)).count()
            non_result = TaskRecord.query.filter(and_(TaskRecord.result == 0, TaskRecord.task_id == task.id)).count()
            unrepaired = TaskRecord.query.filter(and_(TaskRecord.repaired == 0, TaskRecord.task_id == task.id)).count()
            task.success = success
            task.failed = failed
            task.unrepaired = unrepaired
            total = success + failed + non_result
            if total != 0:
                progress = (success + failed) / total
            else:
                progress = 1
            task.progress = round(progress, 2)
            if unrepaired == 0:
                task.status = 1
            db.session.add(task)
            db.session.commit()
            return {"code": 200, "message": "任务刷新成功"}
        else:
            return {"code": 200, "message": "未查询到数据"}

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("pageIndex", type=int)
        parser.add_argument("pageSize", type=int)
        args = parser.parse_args()
        current_page = int(args.get("pageIndex"))
        per_page = int(args.get("pageSize"))
        tasks = Task.query.order_by(Task.created_at.desc()).paginate(page=current_page, per_page=per_page, count=True)
        result = {"code": 200, "total": tasks.total, "list": list()}
        for item in tasks:
            row = dict()
            row["id"] = item.id
            row["name"] = item.name
            row["created_at"] = item.created_at_aware.strftime("%Y-%m-%d %H:%M")
            row["cn_android_total"] = item.cn_android_total
            row["en_android_total"] = item.en_android_total
            row["cn_ios_total"] = item.cn_ios_total
            row["en_ios_total"] = item.en_ios_total
            row["success"] = item.success
            row["failed"] = item.failed
            row["progress"] = str(round(item.progress, 2) * 100) + "%"
            row["status_number"] = item.status
            row["mode"] = item.mode
            if item.status == 0:
                row["status"] = "未开始"
            elif item.status == 1:
                row["status"] = "结束"
            elif item.status == 2:
                row["status"] = "暂停"
            else:
                row["status"] = "进行中"
            row["unrepaired"] = item.unrepaired
            result["list"].append(row)
        return result


class Inspection3(Resource):
    method_decorators = [authenticate]

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("scriptType", type=str)
        args = parser.parse_args()
        script_type = args.get("scriptType")
        if script_type == "All":
            result = work_update({Config.CN_ANDROID: 1, Config.EN_ANDROID: 1, Config.CN_IOS: 1, Config.EN_IOS: 1, Config.CN_ANDROID_IDENTIFY: 1, Config.EN_ANDROID_IDENTIFY: 1})
            return {"code": 200, "message": f"任务下发情况: {str(result)}"}
        if not script_type:
            return {"code": 400, "message": "参数错误"}
        if script_type == Config.CN_ANDROID:
            result = work_update({Config.CN_ANDROID: 1})
        elif script_type == Config.EN_ANDROID:
            result = work_update({Config.EN_ANDROID: 1})
        elif script_type == Config.CN_IOS:
            result = work_update({Config.CN_IOS: 1})
        elif script_type == Config.EN_IOS:
            result = work_update({Config.EN_IOS: 1})
        elif script_type == Config.CN_ANDROID_IDENTIFY:
            result = work_update({Config.CN_ANDROID_IDENTIFY: 1})
        elif script_type == Config.EN_ANDROID_IDENTIFY:
            result = work_update({Config.EN_ANDROID_IDENTIFY: 1})
        else:
            return {"code": 400, "message": "参数错误"}
        return {"code": 200, "message": f"任务下发情况: {str(result)}"}


class Inspection4(Resource):
    method_decorators = [authenticate]

    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument("task_id", type=int, location='args')
        args = parser.parse_args()
        task_id = args.get("task_id")
        if not task_id:
            return {"code": 400, "message": "参数错误"}
        trs = TaskRecord.query.filter(and_(TaskRecord.task_id == task_id,
                                           TaskRecord.result == 2, TaskRecord.repaired == 0)).all()
        result = dict()
        for item in trs:
            if item.platform == 1:
                ca = CnAndroid.query.filter_by(id=item.appid).first()
                if ca:
                    _u = User.query.filter_by(id=ca.user_id).first()
                    if _u:
                        if _u.username not in result:
                            result[_u.username] = {"cn_android": 1, "en_android": 0, "cn_ios": 0, "en_ios": 0}
                        else:
                            result[_u.username]["cn_android"] += 1
            if item.platform == 2:
                ea = EnAndroid.query.filter_by(id=item.appid).first()
                if ea:
                    _u = User.query.filter_by(id=ea.user_id).first()
                    if _u:
                        if _u.username not in result:
                            result[_u.username] = {"cn_android": 0, "en_android": 1, "cn_ios": 0, "en_ios": 0}
                        else:
                            result[_u.username]["en_android"] += 1
            if item.platform == 3:
                ci = CnIOS.query.filter_by(id=item.appid).first()
                if ci:
                    _u = User.query.filter_by(id=ci.user_id).first()
                    if _u:
                        if _u.username not in result:
                            result[_u.username] = {"cn_android": 0, "en_android": 0, "cn_ios": 1, "en_ios": 0}
                        else:
                            result[_u.username]["cn_ios"] += 1
            if item.platform == 4:
                ei = EnIOS.query.filter_by(id=item.appid).first()
                if ei:
                    _u = User.query.filter_by(id=ei.user_id).first()
                    if _u:
                        if _u.username not in result:
                            result[_u.username] = {"cn_android": 0, "en_android": 0, "cn_ios": 0, "en_ios": 1}
                        else:
                            result[_u.username]["en_ios"] += 1
        data = {"code": 200, "list": []}
        for key, value in result.items():
            row = {"author": key, "cn_android": value["cn_android"], "en_android": value["en_android"],
                   "cn_ios": value["cn_ios"], "en_ios": value["en_ios"]}
            data["list"].append(row)
        return data

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("id", type=int)
        parser.add_argument("platform", type=str)
        parser.add_argument("result", type=str)
        parser.add_argument("repaired", type=str)
        parser.add_argument("pageSize", type=int)
        parser.add_argument("pageIndex", type=int)
        args = parser.parse_args()
        _task_id = args.get("id")
        if not _task_id:
            return {"code": 400, "message": "参数错误"}
        query = {"task_id": _task_id}
        _platform = args.get("platform")
        _result = args.get("result")
        _repaired = args.get("repaired")
        per_page = args.get("pageSize")
        current_page = args.get("pageIndex")
        if _platform:
            query["platform"] = int(_platform)
        if _result:
            query["result"] = int(_result)
        if _repaired:
            query["repaired"] = int(_repaired)
        trs = TaskRecord.query.filter_by(**query).paginate(page=current_page, per_page=per_page, count=True)
        data = {
            "code": 200,
            "total": trs.total,
            "list": []
        }
        for item in trs.items:
            row = dict()
            row["l_class"] = ""
            row["author"] = ""
            row["platform"] = ""
            row["id"] = item.id
            row["result"] = "未出结果"
            row["repaired"] = "是" if item.repaired == 1 else "否"
            if item.platform == 1:
                row["platform"] = "Android/cn"
                ca = CnAndroid.query.filter_by(id=item.appid).first()
                if ca:
                    row["l_class"] = ca.l_class
                    _u = User.query.filter_by(id=ca.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 2:
                row["platform"] = "Android/en"
                ea = EnAndroid.query.filter_by(id=item.appid).first()
                if ea:
                    row["l_class"] = ea.l_class
                    _u = User.query.filter_by(id=ea.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 3:
                row["platform"] = "IOS/cn"
                ci = CnIOS.query.filter_by(id=item.appid).first()
                if ci:
                    row["l_class"] = ci.l_class
                    _u = User.query.filter_by(id=ci.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 4:
                row["platform"] = "IOS/en"
                ei = EnIOS.query.filter_by(id=item.appid).first()
                if ei:
                    row["l_class"] = ei.l_class
                    _u = User.query.filter_by(id=ei.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.result == 1:
                row["result"] = "成功"
            elif item.result == 2:
                row["result"] = "失败"
            data["list"].append(row)
        return jsonify(data)


class Inspection5(Resource):
    method_decorators = [authenticate]

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("task_id", type=int)
        parser.add_argument("status_number", type=int)
        args = parser.parse_args()
        task_id = args.get("task_id")
        status_number = args.get("status_number")
        if not task_id or not status_number:
            return {"code": 400, "message": "参数错误"}
        task = Task.query.filter_by(id=task_id).first()
        if task:
            task.status = status_number
            db.session.add(task)
            db.session.commit()
            if status_number == 3 or status_number == 2:
                result = dict()
                if task.cn_android_total:
                    result.update(work_update({Config.CN_ANDROID: 1}))
                if task.en_android_total:
                    result.update(work_update({Config.EN_ANDROID: 1}))
                if task.cn_ios_total:
                    result.update(work_update({Config.CN_IOS: 1}))
                if task.en_ios_total:
                    result.update(work_update({Config.EN_IOS: 1}))
                return {"code": 200, "message": f"任务下发情况: {str(result)}"}
        else:
            return {"code": 200, "message": "未查询到数据"}


class Inspection6(Resource):
    method_decorators = [authenticate]

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("task_id", type=int)
        args = parser.parse_args()
        task_id = args.get("task_id")
        if not task_id:
            return {"code": 400, "message": "参数错误"}
        task = Task.query.filter_by(id=task_id).first()
        TaskRecord.query.filter_by(task_id=task_id).delete()
        if task:
            db.session.delete(task)
            db.session.commit()
            if task.cn_android_total:
                work_update({Config.CN_ANDROID: 1})
            if task.en_android_total:
                work_update({Config.EN_ANDROID: 1})
            if task.cn_ios_total:
                work_update({Config.CN_IOS: 1})
            if task.en_ios_total:
                work_update({Config.EN_IOS: 1})
            return {"code": 200, "message": "删除成功"}
        else:
            return {"code": 200, "message": "未查询到数据"}


class Inspection7(Resource):
    method_decorators = [authenticate]

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("id", type=int)
        parser.add_argument("platform", type=str)
        parser.add_argument("result", type=str)
        parser.add_argument("repaired", type=str)
        args = parser.parse_args()
        _task_id = args.get("id")
        if not _task_id:
            return {"code": 400, "message": "参数错误"}
        query = {"task_id": _task_id}
        _platform = args.get("platform")
        _result = args.get("result")
        _repaired = args.get("repaired")
        if _platform:
            query["platform"] = int(_platform)
        if _result:
            query["result"] = int(_result)
        if _repaired:
            query["repaired"] = int(_repaired)
        trs = TaskRecord.query.filter_by(**query).all()
        data = {
            "code": 200,
            "list": []
        }
        for item in trs:
            row = dict()
            row["l_class"] = ""
            row["author"] = ""
            row["platform"] = ""
            row["id"] = item.id
            row["result"] = "未出结果"
            row["repaired"] = "是" if item.repaired == 1 else "否"
            if item.platform == 1:
                row["platform"] = "Android/cn"
                ca = CnAndroid.query.filter_by(id=item.appid).first()
                if ca:
                    row["l_class"] = ca.l_class
                    _u = User.query.filter_by(id=ca.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 2:
                row["platform"] = "Android/en"
                ea = EnAndroid.query.filter_by(id=item.appid).first()
                if ea:
                    row["l_class"] = ea.l_class
                    _u = User.query.filter_by(id=ea.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 3:
                row["platform"] = "IOS/cn"
                ci = CnIOS.query.filter_by(id=item.appid).first()
                if ci:
                    row["l_class"] = ci.l_class
                    _u = User.query.filter_by(id=ci.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 4:
                row["platform"] = "IOS/en"
                ei = EnIOS.query.filter_by(id=item.appid).first()
                if ei:
                    row["l_class"] = ei.l_class
                    _u = User.query.filter_by(id=ei.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.result == 1:
                row["result"] = "成功"
            elif item.result == 2:
                row["result"] = "失败"
            data["list"].append(row)
        file_name = write_task_detail_table(data["list"])
        file_url = '/api' + url_for('extractapptable', file_name=file_name)
        return {"code": 200, "message": "文件生成成功", "link": file_url}


class TaskLink(Resource):
    method_decorators = [authenticate]

    def get(self):
        return {"code": 200, "message": '/api' + url_for("tasktemplate")}


class TaskTemplate(Resource):
    # method_decorators = [authenticate]

    def get(self):
        _f = task_template()
        return send_file(_f, as_attachment=True)


