# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   inspection.py
@Time    :   2024/4/11 13:18
"""
import os
import datetime
from flask import jsonify, url_for, send_file
from flask_restful import Resource, reqparse
from werkzeug.datastructures import FileStorage
from sqlalchemy import and_
from app import db
from app.models import CnAndroid, EnAndroid, CnIOS, EnIOS, InspectionRecord, Task, TaskRecord, User
from app.resources.user import authenticate
from app.utils import work_update, task_template, read_task_file, write_task_detail_table
from config import Config


class Inspection(Resource):
    method_decorators = [authenticate]

    @staticmethod
    def _parse_arguments() -> Dict[str, Any]:
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, location="form")
        parser.add_argument("platform", type=str, location="form")
        parser.add_argument("mode", type=str, location="form")
        parser.add_argument('file', type=FileStorage, location='files')
        args = parser.parse_args()

        return {
            'name': args.get("name"),
            'platforms': args.get("platform", ""),
            'mode': args.get("mode", ""),
            'file': args.get('file')
        }

    def post(self) -> Dict[str, Any]:
        try:
            args = self._parse_arguments()

            validation_result = self._validate_parameters(args)
            if validation_result:
                return validation_result

            task = self._create_task(args['name'], args['mode'])

            if args['platforms']:
                self._process_platform_tasks(args, task)

            if args['file']:
                file_result = self._process_file_task(args['file'], args['mode'], task)
                if not file_result:
                    db.session.rollback()
                    return {"code": 400, "message": "任务文件异常"}

            db.session.commit()
            return {"code": 200, "message": "新增日常巡检成功！"}

        except Exception as e:
            db.session.rollback()
            logging.error(f"创建巡检任务失败: {str(e)}")
            return {"code": 500, "message": "数据库操作异常"}



    @staticmethod
    def _validate_parameters(args: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        if not args['name'] or (not args['platforms'] and not args['file']):
            return {"code": 400, "message": "参数错误"}

        if args['mode'] not in ['block', 'identify']:
            return {"code": 400, "message": "mode参数必须是block或identify"}

        existing_task = Task.query.filter_by(name=args['name']).first()
        if existing_task:
            return {"code": 400, "message": "任务名称重复！"}

        return None

    @staticmethod
    def _create_task(name: str, mode: str) -> Task:
        task = Task(name=name, mode=mode)
        db.session.add(task)
        db.session.flush()
        return task

    @staticmethod
    def _build_query_conditions(model, mode: str, l_class=None):
        conditions = [model.result == "成功", model.delete == 0]
        if l_class is not None:
            conditions.append(model.l_class == l_class)

        if mode == 'block':
            conditions.append(model.block_script == 1)
        else:
            conditions.append(model.identify_script == 1)

        return and_(*conditions)

    @staticmethod
    def _create_task_records(items, platform_id: int, task: Task):
        for item in items:
            task_record = TaskRecord(appid=item.id, platform=platform_id)
            task.task_record.append(task_record)

    def _process_platform_generic(self, platform_key: str, mode: str, task: Task) -> None:
        platform_configs = {
            Config.CN_ANDROID: {
                'model': CnAndroid,
                'platform_id': 1,
                'total_field': 'cn_android_total'
            },
            Config.EN_ANDROID: {
                'model': EnAndroid,
                'platform_id': 2,
                'total_field': 'en_android_total'
            },
            Config.CN_IOS: {
                'model': CnIOS,
                'platform_id': 3,
                'total_field': 'cn_ios_total'
            },
            Config.EN_IOS: {
                'model': EnIOS,
                'platform_id': 4,
                'total_field': 'en_ios_total'
            }
        }

        config = platform_configs[platform_key]
        model = config['model']
        platform_id = config['platform_id']
        total_field = config['total_field']

        conditions = self._build_query_conditions(model, mode)
        items = model.query.filter(conditions).all()

        setattr(task, total_field, len(items))
        self._create_task_records(items, platform_id, task)

    def _process_platform_tasks(self, args: Dict[str, Any], task: Task) -> None:
        platforms = args['platforms'].split("+")
        for platform_key in platforms:
            if platform_key in [Config.CN_ANDROID, Config.EN_ANDROID, Config.CN_IOS, Config.EN_IOS]:
                self._process_platform_generic(platform_key, args['mode'], task)



    def _process_file_task(self, file: FileStorage, mode: str, task: Task) -> bool:
        try:
            file_path = self._save_uploaded_file(file)
            if not file_path:
                return False
            data = read_task_file(file_path)
            if not self._has_valid_data(data):
                return True

            self._process_file_platforms(data, mode, task)

            return True

        except Exception as e:
            logging.error(f"处理文件任务失败: {str(e)}")
            return False

    @staticmethod
    def _save_uploaded_file(file: FileStorage) -> Optional[str]:
        try:
            filename = f"{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}-" + file.filename
            path = os.path.join(Config.BASEDIR, 'tmp', filename)
            file.save(path)
            return path
        except Exception as e:
            logging.error(f"保存文件失败: {str(e)}")
            return None

    @staticmethod
    def _has_valid_data(data: Dict[str, List[str]]) -> List[str]:
        return (data[Config.CN_ANDROID] or data[Config.EN_ANDROID] or
                data[Config.CN_IOS] or data[Config.EN_IOS])

    def _process_file_platforms(self, data: Dict[str, List[str]], mode: str, task: Task) -> None:
        platform_keys = [Config.CN_ANDROID, Config.EN_ANDROID, Config.CN_IOS, Config.EN_IOS]
        for platform_key in platform_keys:
            if data[platform_key]:
                self._process_file_platform_generic(data[platform_key], platform_key, mode, task)

    def _process_file_platform_generic(self, l_class_list: List[str], platform_key: str, mode: str, task: Task) -> None:

        platform_configs = {
            Config.CN_ANDROID: {
                'model': CnAndroid,
                'platform_id': 1,
                'total_field': 'cn_android_total'
            },
            Config.EN_ANDROID: {
                'model': EnAndroid,
                'platform_id': 2,
                'total_field': 'en_android_total'
            },
            Config.CN_IOS: {
                'model': CnIOS,
                'platform_id': 3,
                'total_field': 'cn_ios_total'
            },
            Config.EN_IOS: {
                'model': EnIOS,
                'platform_id': 4,
                'total_field': 'en_ios_total'
            }
        }

        config = platform_configs[platform_key]
        model = config['model']
        platform_id = config['platform_id']
        total_field = config['total_field']

        current_total = getattr(task, total_field)

        for l_class in l_class_list:
            conditions = self._build_query_conditions(model, mode, l_class)
            item = model.query.filter(conditions).first()

            if item:
                current_total += 1
                task_record = TaskRecord(appid=item.id, platform=platform_id)
                task.task_record.append(task_record)

        setattr(task, total_field, current_total)


class Inspection2(Resource):
    method_decorators = [authenticate]

    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument("task_id", type=int, location='args')
        args = parser.parse_args()
        task_id = args.get("task_id")
        if not task_id:
            return {"code": 400, "message": "参数错误"}
        task = Task.query.filter_by(id=task_id).first()
        if task:
            success = TaskRecord.query.filter(and_(TaskRecord.result == 1, TaskRecord.task_id == task.id)).count()
            failed = TaskRecord.query.filter(and_(TaskRecord.result == 2, TaskRecord.task_id == task.id)).count()
            non_result = TaskRecord.query.filter(and_(TaskRecord.result == 0, TaskRecord.task_id == task.id)).count()
            unrepaired = TaskRecord.query.filter(and_(TaskRecord.repaired == 0, TaskRecord.task_id == task.id)).count()
            task.success = success
            task.failed = failed
            task.unrepaired = unrepaired
            total = success + failed + non_result
            if total != 0:
                progress = (success + failed) / total
            else:
                progress = 1
            task.progress = round(progress, 2)
            if unrepaired == 0:
                task.status = 1
            db.session.add(task)
            db.session.commit()
            return {"code": 200, "message": "任务刷新成功"}
        else:
            return {"code": 200, "message": "未查询到数据"}

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("pageIndex", type=int)
        parser.add_argument("pageSize", type=int)
        args = parser.parse_args()
        current_page = int(args.get("pageIndex"))
        per_page = int(args.get("pageSize"))
        tasks = Task.query.order_by(Task.created_at.desc()).paginate(page=current_page, per_page=per_page, count=True)
        result = {"code": 200, "total": tasks.total, "list": list()}
        for item in tasks:
            row = dict()
            row["id"] = item.id
            row["name"] = item.name
            row["created_at"] = item.created_at_aware.strftime("%Y-%m-%d %H:%M")
            row["cn_android_total"] = item.cn_android_total
            row["en_android_total"] = item.en_android_total
            row["cn_ios_total"] = item.cn_ios_total
            row["en_ios_total"] = item.en_ios_total
            row["success"] = item.success
            row["failed"] = item.failed
            row["progress"] = str(round(item.progress, 2) * 100) + "%"
            row["status_number"] = item.status
            row["mode"] = item.mode
            if item.status == 0:
                row["status"] = "未开始"
            elif item.status == 1:
                row["status"] = "结束"
            elif item.status == 2:
                row["status"] = "暂停"
            else:
                row["status"] = "进行中"
            row["unrepaired"] = item.unrepaired
            result["list"].append(row)
        return result


class Inspection3(Resource):
    method_decorators = [authenticate]

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("scriptType", type=str)
        args = parser.parse_args()
        script_type = args.get("scriptType")
        if script_type == "All":
            result = work_update({Config.CN_ANDROID: 1, Config.EN_ANDROID: 1, Config.CN_IOS: 1, Config.EN_IOS: 1, Config.CN_ANDROID_IDENTIFY: 1, Config.EN_ANDROID_IDENTIFY: 1})
            return {"code": 200, "message": f"任务下发情况: {str(result)}"}
        if not script_type:
            return {"code": 400, "message": "参数错误"}
        if script_type == Config.CN_ANDROID:
            result = work_update({Config.CN_ANDROID: 1})
        elif script_type == Config.EN_ANDROID:
            result = work_update({Config.EN_ANDROID: 1})
        elif script_type == Config.CN_IOS:
            result = work_update({Config.CN_IOS: 1})
        elif script_type == Config.EN_IOS:
            result = work_update({Config.EN_IOS: 1})
        elif script_type == Config.CN_ANDROID_IDENTIFY:
            result = work_update({Config.CN_ANDROID_IDENTIFY: 1})
        elif script_type == Config.EN_ANDROID_IDENTIFY:
            result = work_update({Config.EN_ANDROID_IDENTIFY: 1})
        else:
            return {"code": 400, "message": "参数错误"}
        return {"code": 200, "message": f"任务下发情况: {str(result)}"}


class Inspection4(Resource):
    method_decorators = [authenticate]

    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument("task_id", type=int, location='args')
        args = parser.parse_args()
        task_id = args.get("task_id")
        if not task_id:
            return {"code": 400, "message": "参数错误"}
        trs = TaskRecord.query.filter(and_(TaskRecord.task_id == task_id,
                                           TaskRecord.result == 2, TaskRecord.repaired == 0)).all()
        result = dict()
        for item in trs:
            if item.platform == 1:
                ca = CnAndroid.query.filter_by(id=item.appid).first()
                if ca:
                    _u = User.query.filter_by(id=ca.user_id).first()
                    if _u:
                        if _u.username not in result:
                            result[_u.username] = {"cn_android": 1, "en_android": 0, "cn_ios": 0, "en_ios": 0}
                        else:
                            result[_u.username]["cn_android"] += 1
            if item.platform == 2:
                ea = EnAndroid.query.filter_by(id=item.appid).first()
                if ea:
                    _u = User.query.filter_by(id=ea.user_id).first()
                    if _u:
                        if _u.username not in result:
                            result[_u.username] = {"cn_android": 0, "en_android": 1, "cn_ios": 0, "en_ios": 0}
                        else:
                            result[_u.username]["en_android"] += 1
            if item.platform == 3:
                ci = CnIOS.query.filter_by(id=item.appid).first()
                if ci:
                    _u = User.query.filter_by(id=ci.user_id).first()
                    if _u:
                        if _u.username not in result:
                            result[_u.username] = {"cn_android": 0, "en_android": 0, "cn_ios": 1, "en_ios": 0}
                        else:
                            result[_u.username]["cn_ios"] += 1
            if item.platform == 4:
                ei = EnIOS.query.filter_by(id=item.appid).first()
                if ei:
                    _u = User.query.filter_by(id=ei.user_id).first()
                    if _u:
                        if _u.username not in result:
                            result[_u.username] = {"cn_android": 0, "en_android": 0, "cn_ios": 0, "en_ios": 1}
                        else:
                            result[_u.username]["en_ios"] += 1
        data = {"code": 200, "list": []}
        for key, value in result.items():
            row = {"author": key, "cn_android": value["cn_android"], "en_android": value["en_android"],
                   "cn_ios": value["cn_ios"], "en_ios": value["en_ios"]}
            data["list"].append(row)
        return data

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("id", type=int)
        parser.add_argument("platform", type=str)
        parser.add_argument("result", type=str)
        parser.add_argument("repaired", type=str)
        parser.add_argument("pageSize", type=int)
        parser.add_argument("pageIndex", type=int)
        args = parser.parse_args()
        _task_id = args.get("id")
        if not _task_id:
            return {"code": 400, "message": "参数错误"}
        query = {"task_id": _task_id}
        _platform = args.get("platform")
        _result = args.get("result")
        _repaired = args.get("repaired")
        per_page = args.get("pageSize")
        current_page = args.get("pageIndex")
        if _platform:
            query["platform"] = int(_platform)
        if _result:
            query["result"] = int(_result)
        if _repaired:
            query["repaired"] = int(_repaired)
        trs = TaskRecord.query.filter_by(**query).paginate(page=current_page, per_page=per_page, count=True)
        data = {
            "code": 200,
            "total": trs.total,
            "list": []
        }
        for item in trs.items:
            row = dict()
            row["l_class"] = ""
            row["author"] = ""
            row["platform"] = ""
            row["id"] = item.id
            row["result"] = "未出结果"
            row["repaired"] = "是" if item.repaired == 1 else "否"
            if item.platform == 1:
                row["platform"] = "Android/cn"
                ca = CnAndroid.query.filter_by(id=item.appid).first()
                if ca:
                    row["l_class"] = ca.l_class
                    _u = User.query.filter_by(id=ca.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 2:
                row["platform"] = "Android/en"
                ea = EnAndroid.query.filter_by(id=item.appid).first()
                if ea:
                    row["l_class"] = ea.l_class
                    _u = User.query.filter_by(id=ea.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 3:
                row["platform"] = "IOS/cn"
                ci = CnIOS.query.filter_by(id=item.appid).first()
                if ci:
                    row["l_class"] = ci.l_class
                    _u = User.query.filter_by(id=ci.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 4:
                row["platform"] = "IOS/en"
                ei = EnIOS.query.filter_by(id=item.appid).first()
                if ei:
                    row["l_class"] = ei.l_class
                    _u = User.query.filter_by(id=ei.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.result == 1:
                row["result"] = "成功"
            elif item.result == 2:
                row["result"] = "失败"
            data["list"].append(row)
        return jsonify(data)


class Inspection5(Resource):
    method_decorators = [authenticate]

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("task_id", type=int)
        parser.add_argument("status_number", type=int)
        args = parser.parse_args()
        task_id = args.get("task_id")
        status_number = args.get("status_number")
        if not task_id or not status_number:
            return {"code": 400, "message": "参数错误"}
        task = Task.query.filter_by(id=task_id).first()
        if task:
            task.status = status_number
            db.session.add(task)
            db.session.commit()
            if status_number == 3 or status_number == 2:
                result = dict()
                if task.cn_android_total:
                    result.update(work_update({Config.CN_ANDROID: 1}))
                if task.en_android_total:
                    result.update(work_update({Config.EN_ANDROID: 1}))
                if task.cn_ios_total:
                    result.update(work_update({Config.CN_IOS: 1}))
                if task.en_ios_total:
                    result.update(work_update({Config.EN_IOS: 1}))
                return {"code": 200, "message": f"任务下发情况: {str(result)}"}
        else:
            return {"code": 200, "message": "未查询到数据"}


class Inspection6(Resource):
    method_decorators = [authenticate]

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("task_id", type=int)
        args = parser.parse_args()
        task_id = args.get("task_id")
        if not task_id:
            return {"code": 400, "message": "参数错误"}
        task = Task.query.filter_by(id=task_id).first()
        TaskRecord.query.filter_by(task_id=task_id).delete()
        if task:
            db.session.delete(task)
            db.session.commit()
            if task.cn_android_total:
                work_update({Config.CN_ANDROID: 1})
            if task.en_android_total:
                work_update({Config.EN_ANDROID: 1})
            if task.cn_ios_total:
                work_update({Config.CN_IOS: 1})
            if task.en_ios_total:
                work_update({Config.EN_IOS: 1})
            return {"code": 200, "message": "删除成功"}
        else:
            return {"code": 200, "message": "未查询到数据"}


class Inspection7(Resource):
    method_decorators = [authenticate]

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("id", type=int)
        parser.add_argument("platform", type=str)
        parser.add_argument("result", type=str)
        parser.add_argument("repaired", type=str)
        args = parser.parse_args()
        _task_id = args.get("id")
        if not _task_id:
            return {"code": 400, "message": "参数错误"}
        query = {"task_id": _task_id}
        _platform = args.get("platform")
        _result = args.get("result")
        _repaired = args.get("repaired")
        if _platform:
            query["platform"] = int(_platform)
        if _result:
            query["result"] = int(_result)
        if _repaired:
            query["repaired"] = int(_repaired)
        trs = TaskRecord.query.filter_by(**query).all()
        data = {
            "code": 200,
            "list": []
        }
        for item in trs:
            row = dict()
            row["l_class"] = ""
            row["author"] = ""
            row["platform"] = ""
            row["id"] = item.id
            row["result"] = "未出结果"
            row["repaired"] = "是" if item.repaired == 1 else "否"
            if item.platform == 1:
                row["platform"] = "Android/cn"
                ca = CnAndroid.query.filter_by(id=item.appid).first()
                if ca:
                    row["l_class"] = ca.l_class
                    _u = User.query.filter_by(id=ca.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 2:
                row["platform"] = "Android/en"
                ea = EnAndroid.query.filter_by(id=item.appid).first()
                if ea:
                    row["l_class"] = ea.l_class
                    _u = User.query.filter_by(id=ea.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 3:
                row["platform"] = "IOS/cn"
                ci = CnIOS.query.filter_by(id=item.appid).first()
                if ci:
                    row["l_class"] = ci.l_class
                    _u = User.query.filter_by(id=ci.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.platform == 4:
                row["platform"] = "IOS/en"
                ei = EnIOS.query.filter_by(id=item.appid).first()
                if ei:
                    row["l_class"] = ei.l_class
                    _u = User.query.filter_by(id=ei.user_id).first()
                    if _u:
                        row["author"] = _u.username
            if item.result == 1:
                row["result"] = "成功"
            elif item.result == 2:
                row["result"] = "失败"
            data["list"].append(row)
        file_name = write_task_detail_table(data["list"])
        file_url = '/api' + url_for('extractapptable', file_name=file_name)
        return {"code": 200, "message": "文件生成成功", "link": file_url}


class TaskLink(Resource):
    method_decorators = [authenticate]

    def get(self):
        return {"code": 200, "message": '/api' + url_for("tasktemplate")}


class TaskTemplate(Resource):
    # method_decorators = [authenticate]

    def get(self):
        _f = task_template()
        return send_file(_f, as_attachment=True)


